import { describe, it, expect, beforeEach, vi } from 'vitest'
import { renderHook, act } from '@testing-library/react'
import { useRetirementCalculations } from '../useRetirementCalculations'
import { useAppStore } from '@/stores/useAppStore'

// Mock the store
vi.mock('@/stores/useAppStore')

// Mock the calculation utilities
vi.mock('@/utils/retirementCalculations', () => ({
  calculateProjections: vi.fn().mockReturnValue({
    yearlyProjections: [
      {
        age: 30,
        year: 2024,
        portfolioValue: 1000000,
        realValue: 1000000,
        cumulativeContributions: 500000,
      },
      {
        age: 65,
        year: 2059,
        portfolioValue: 10000000,
        realValue: 8000000,
        cumulativeContributions: 2000000,
      },
    ],
    finalValue: 10000000,
    totalContributions: 2000000,
    monthlyRetirementIncome: 83333,
    realMonthlyIncome: 66667,
    currentAge: 30,
  }),
}))

describe('useRetirementCalculations', () => {
  const mockStore = {
    retirementData: {
      currentAge: 30,
      retirementAge: 65,
      currentPortfolio: 1000000,
      monthlyContribution: 50000,
      annualReturn: 12,
      inflationRate: 3,
      contributionGrowthRate: 2,
      emergencyFund: 500000,
    },
    updateRetirementData: vi.fn(),
  }

  beforeEach(() => {
    vi.clearAllMocks()
    ;(useAppStore as any).mockReturnValue(mockStore)
  })

  it('should initialize with retirement data from store', () => {
    const { result } = renderHook(() => useRetirementCalculations())

    expect(result.current.data).toEqual(mockStore.retirementData)
  })

  it('should calculate projections on mount', () => {
    const { result } = renderHook(() => useRetirementCalculations())

    expect(result.current.projections).toBeDefined()
    expect(result.current.projections.yearlyProjections).toHaveLength(2)
    expect(result.current.projections.finalValue).toBe(10000000)
  })

  it('should handle input changes', () => {
    const { result } = renderHook(() => useRetirementCalculations())

    act(() => {
      result.current.handleInputChange('currentAge', 35)
    })

    expect(mockStore.updateRetirementData).toHaveBeenCalledWith('currentAge', 35)
  })

  it('should handle numeric input changes', () => {
    const { result } = renderHook(() => useRetirementCalculations())

    act(() => {
      result.current.handleInputChange('monthlyContribution', 75000)
    })

    expect(mockStore.updateRetirementData).toHaveBeenCalledWith('monthlyContribution', 75000)
  })

  it('should recalculate projections when data changes', () => {
    const { result, rerender } = renderHook(() => useRetirementCalculations())

    const initialProjections = result.current.projections

    // Update the mock store data
    const updatedStore = {
      ...mockStore,
      retirementData: {
        ...mockStore.retirementData,
        currentAge: 35,
      },
    }
    ;(useAppStore as any).mockReturnValue(updatedStore)

    rerender()

    // Should recalculate with new data
    expect(result.current.data.currentAge).toBe(35)
  })

  it('should handle edge cases in input values', () => {
    const { result } = renderHook(() => useRetirementCalculations())

    act(() => {
      result.current.handleInputChange('annualReturn', 0)
    })

    expect(mockStore.updateRetirementData).toHaveBeenCalledWith('annualReturn', 0)

    act(() => {
      result.current.handleInputChange('currentPortfolio', -1000)
    })

    expect(mockStore.updateRetirementData).toHaveBeenCalledWith('currentPortfolio', -1000)
  })

  it('should handle string input values correctly', () => {
    const { result } = renderHook(() => useRetirementCalculations())

    act(() => {
      result.current.handleInputChange('currentAge', '40' as any)
    })

    expect(mockStore.updateRetirementData).toHaveBeenCalledWith('currentAge', '40')
  })

  it('should maintain referential stability for handlers', () => {
    const { result, rerender } = renderHook(() => useRetirementCalculations())

    const firstHandler = result.current.handleInputChange

    rerender()

    const secondHandler = result.current.handleInputChange

    expect(firstHandler).toBe(secondHandler)
  })

  describe('error handling', () => {
    it('should handle calculation errors gracefully', () => {
      // Mock calculation to throw error
      vi.mocked(require('@/utils/retirementCalculations').calculateProjections).mockImplementation(() => {
        throw new Error('Calculation error')
      })

      expect(() => {
        renderHook(() => useRetirementCalculations())
      }).not.toThrow()
    })

    it('should handle missing store data', () => {
      const incompleteStore = {
        retirementData: {
          currentAge: 30,
          // Missing other required fields
        },
        updateRetirementData: vi.fn(),
      }
      ;(useAppStore as any).mockReturnValue(incompleteStore)

      expect(() => {
        renderHook(() => useRetirementCalculations())
      }).not.toThrow()
    })
  })

  describe('performance', () => {
    it('should memoize projections calculation', () => {
      const calculateProjections = vi.mocked(require('@/utils/retirementCalculations').calculateProjections)
      calculateProjections.mockClear()

      const { result, rerender } = renderHook(() => useRetirementCalculations())

      expect(calculateProjections).toHaveBeenCalledTimes(1)

      // Rerender without changing data
      rerender()

      // Should not recalculate
      expect(calculateProjections).toHaveBeenCalledTimes(1)
    })

    it('should recalculate only when relevant data changes', () => {
      const calculateProjections = vi.mocked(require('@/utils/retirementCalculations').calculateProjections)
      calculateProjections.mockClear()

      const { result } = renderHook(() => useRetirementCalculations())

      expect(calculateProjections).toHaveBeenCalledTimes(1)

      // Change data that affects calculations
      act(() => {
        result.current.handleInputChange('currentAge', 35)
      })

      // Should trigger recalculation on next render
      expect(calculateProjections).toHaveBeenCalledTimes(1) // Still 1 because we haven't re-rendered yet
    })
  })

  describe('data validation', () => {
    it('should handle invalid retirement age scenarios', () => {
      const invalidStore = {
        ...mockStore,
        retirementData: {
          ...mockStore.retirementData,
          currentAge: 65,
          retirementAge: 60, // Invalid: retirement before current age
        },
      }
      ;(useAppStore as any).mockReturnValue(invalidStore)

      const { result } = renderHook(() => useRetirementCalculations())

      expect(result.current.data.currentAge).toBe(65)
      expect(result.current.data.retirementAge).toBe(60)
      // Hook should still work, validation should be handled elsewhere
    })

    it('should handle extreme values', () => {
      const extremeStore = {
        ...mockStore,
        retirementData: {
          ...mockStore.retirementData,
          annualReturn: 1000, // 1000% return
          inflationRate: -50, // Negative inflation
          currentPortfolio: 1e15, // Very large portfolio
        },
      }
      ;(useAppStore as any).mockReturnValue(extremeStore)

      expect(() => {
        renderHook(() => useRetirementCalculations())
      }).not.toThrow()
    })
  })

  describe('integration with store', () => {
    it('should reflect store updates immediately', () => {
      const { result } = renderHook(() => useRetirementCalculations())

      expect(result.current.data.currentAge).toBe(30)

      // Simulate store update
      const newStore = {
        ...mockStore,
        retirementData: {
          ...mockStore.retirementData,
          currentAge: 35,
        },
      }
      ;(useAppStore as any).mockReturnValue(newStore)

      const { result: newResult } = renderHook(() => useRetirementCalculations())

      expect(newResult.current.data.currentAge).toBe(35)
    })

    it('should call store update methods correctly', () => {
      const { result } = renderHook(() => useRetirementCalculations())

      act(() => {
        result.current.handleInputChange('monthlyContribution', 100000)
      })

      expect(mockStore.updateRetirementData).toHaveBeenCalledWith('monthlyContribution', 100000)
      expect(mockStore.updateRetirementData).toHaveBeenCalledTimes(1)
    })
  })
})
