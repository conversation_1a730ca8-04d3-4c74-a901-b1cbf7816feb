import { useMemo } from "react";
import { useAppStore } from "@/stores/useAppStore";
import { calculateRetirementProjection } from "@/utils/retirementCalculations";

export const useRetirementCalculations = () => {
  const { retirementData: data, updateRetirementField: updateField } =
    useAppStore();

  const projections = useMemo(() => {
    const result = calculateRetirementProjection(data);
    console.log("Retirement projections calculated:", result);
    return result;
  }, [data]);

  const handleInputChange = (field: keyof typeof data, value: string) => {
    const numValue = parseFloat(value) || 0;
    updateField(field, numValue);
  };

  return {
    data,
    projections,
    handleInputChange,
  };
};
