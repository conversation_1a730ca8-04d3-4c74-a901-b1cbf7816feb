
import { ExpenseCategory } from '@/types/costCalculation';

export const defaultExpenses: ExpenseCategory[] = [
  { id: '1', name: 'Food & Groceries', iconName: 'Utensils', monthlyAmount: 15000, frequency: 'monthly' },
  { id: '2', name: 'Healthcare', iconName: 'Heart', monthlyAmount: 8000, frequency: 'monthly' },
  { id: '3', name: 'Utilities & Bills', iconName: 'Home', monthlyAmount: 5000, frequency: 'monthly' },
  { id: '4', name: 'Clothing', iconName: 'Shirt', monthlyAmount: 3000, frequency: 'monthly' },
  { id: '5', name: 'Mobile Phone', iconName: 'Smartphone', monthlyAmount: 2000, frequency: 'monthly' },
  { id: '6', name: 'Vacation', iconName: 'Plane', monthlyAmount: 25000, frequency: 'yearly' },
  { id: '7', name: 'Laptop', iconName: 'Laptop', monthlyAmount: 60000, frequency: 'one-time', yearsInterval: 3 },
  { id: '8', name: 'Car', iconName: 'Car', monthlyAmount: 800000, frequency: 'one-time', yearsInterval: 8 },
  { id: '9', name: 'Furniture', iconName: 'Sofa', monthlyAmount: 50000, frequency: 'one-time', yearsInterval: 10 },
  { id: '10', name: 'TV/Electronics', iconName: 'Tv', monthlyAmount: 40000, frequency: 'one-time', yearsInterval: 5 },
  { id: '11', name: 'Home Improvement', iconName: 'Home', monthlyAmount: 100000, frequency: 'yearly' },
];
