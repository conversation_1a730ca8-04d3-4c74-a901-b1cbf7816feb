import { describe, it, expect, beforeEach, vi } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { <PERSON><PERSON><PERSON><PERSON>outer } from 'react-router-dom'
import App from '../App'
import { useAppStore } from '@/stores/useAppStore'

// Mock Recharts to avoid canvas issues in tests
vi.mock('recharts', () => ({
  LineChart: ({ children }: any) => <div data-testid="line-chart">{children}</div>,
  Line: () => <div data-testid="line" />,
  XAxis: () => <div data-testid="x-axis" />,
  YAxis: () => <div data-testid="y-axis" />,
  CartesianGrid: () => <div data-testid="grid" />,
  Tooltip: () => <div data-testid="tooltip" />,
  Legend: () => <div data-testid="legend" />,
  ResponsiveContainer: ({ children }: any) => <div data-testid="responsive-container">{children}</div>,
  AreaChart: ({ children }: any) => <div data-testid="area-chart">{children}</div>,
  Area: () => <div data-testid="area" />,
  Scatter: () => <div data-testid="scatter" />,
  ReferenceDot: () => <div data-testid="reference-dot" />,
}))

// Test wrapper component
const TestWrapper = ({ children }: { children: React.ReactNode }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  })

  return (
    <QueryClientProvider client={queryClient}>
      <BrowserRouter>
        {children}
      </BrowserRouter>
    </QueryClientProvider>
  )
}

describe('Integration Tests', () => {
  beforeEach(() => {
    // Reset store to defaults before each test
    useAppStore.getState().resetToDefaults()
  })

  describe('App Component', () => {
    it('should render the main application', () => {
      render(
        <TestWrapper>
          <App />
        </TestWrapper>
      )

      // Should render without crashing
      expect(document.body).toBeInTheDocument()
    })

    it('should handle routing correctly', () => {
      render(
        <TestWrapper>
          <App />
        </TestWrapper>
      )

      // Should render the main page content
      expect(screen.getByText(/nest egg visionary/i)).toBeInTheDocument()
    })
  })

  describe('End-to-End Workflow', () => {
    it('should allow complete retirement planning workflow', async () => {
      const user = userEvent.setup()
      
      render(
        <TestWrapper>
          <App />
        </TestWrapper>
      )

      // 1. Update retirement parameters
      const currentAgeInput = screen.getByLabelText(/current age/i)
      await user.clear(currentAgeInput)
      await user.type(currentAgeInput, '35')

      const retirementAgeInput = screen.getByLabelText(/retirement age/i)
      await user.clear(retirementAgeInput)
      await user.type(retirementAgeInput, '65')

      // 2. Update portfolio information
      const portfolioInput = screen.getByLabelText(/current portfolio/i)
      await user.clear(portfolioInput)
      await user.type(portfolioInput, '2000000')

      // 3. Add a new expense
      const addExpenseButton = screen.getByText(/add expense/i)
      await user.click(addExpenseButton)

      // Fill out expense form
      const expenseNameInput = screen.getByLabelText(/expense name/i)
      await user.type(expenseNameInput, 'Test Expense')

      const expenseAmountInput = screen.getByLabelText(/monthly amount/i)
      await user.type(expenseAmountInput, '5000')

      const saveExpenseButton = screen.getByText(/save expense/i)
      await user.click(saveExpenseButton)

      // 4. Verify calculations are updated
      await waitFor(() => {
        expect(screen.getByText('Test Expense')).toBeInTheDocument()
      })

      // 5. Check that charts are rendered
      expect(screen.getByTestId('line-chart')).toBeInTheDocument()
    })

    it('should handle expense management workflow', async () => {
      const user = userEvent.setup()
      
      render(
        <TestWrapper>
          <App />
        </TestWrapper>
      )

      // Get initial expense count
      const store = useAppStore.getState()
      const initialExpenseCount = store.expenses.length

      // Add new expense
      const addExpenseButton = screen.getByText(/add expense/i)
      await user.click(addExpenseButton)

      const expenseNameInput = screen.getByLabelText(/expense name/i)
      await user.type(expenseNameInput, 'New Test Expense')

      const expenseAmountInput = screen.getByLabelText(/monthly amount/i)
      await user.type(expenseAmountInput, '10000')

      const saveExpenseButton = screen.getByText(/save expense/i)
      await user.click(saveExpenseButton)

      // Verify expense was added
      await waitFor(() => {
        const updatedStore = useAppStore.getState()
        expect(updatedStore.expenses.length).toBe(initialExpenseCount + 1)
      })

      // Edit the expense
      const editButton = screen.getByLabelText(/edit new test expense/i)
      await user.click(editButton)

      const editAmountInput = screen.getByDisplayValue('10000')
      await user.clear(editAmountInput)
      await user.type(editAmountInput, '15000')

      // Verify expense was updated
      await waitFor(() => {
        const updatedStore = useAppStore.getState()
        const newExpense = updatedStore.expenses.find(e => e.name === 'New Test Expense')
        expect(newExpense?.monthlyAmount).toBe(15000)
      })

      // Remove the expense
      const removeButton = screen.getByLabelText(/remove new test expense/i)
      await user.click(removeButton)

      // Verify expense was removed
      await waitFor(() => {
        const updatedStore = useAppStore.getState()
        expect(updatedStore.expenses.length).toBe(initialExpenseCount)
      })
    })

    it('should handle different expense frequencies', async () => {
      const user = userEvent.setup()
      
      render(
        <TestWrapper>
          <App />
        </TestWrapper>
      )

      // Add yearly expense
      const addExpenseButton = screen.getByText(/add expense/i)
      await user.click(addExpenseButton)

      const expenseNameInput = screen.getByLabelText(/expense name/i)
      await user.type(expenseNameInput, 'Annual Insurance')

      const expenseAmountInput = screen.getByLabelText(/monthly amount/i)
      await user.type(expenseAmountInput, '50000')

      const frequencySelect = screen.getByLabelText(/frequency/i)
      await user.selectOptions(frequencySelect, 'yearly')

      const saveExpenseButton = screen.getByText(/save expense/i)
      await user.click(saveExpenseButton)

      // Verify yearly expense was added
      await waitFor(() => {
        const store = useAppStore.getState()
        const yearlyExpense = store.expenses.find(e => e.name === 'Annual Insurance')
        expect(yearlyExpense?.frequency).toBe('yearly')
      })

      // Add one-time expense
      await user.click(addExpenseButton)

      await user.type(screen.getByLabelText(/expense name/i), 'Car Purchase')
      await user.type(screen.getByLabelText(/monthly amount/i), '500000')
      await user.selectOptions(screen.getByLabelText(/frequency/i), 'one-time')

      const intervalInput = screen.getByLabelText(/years interval/i)
      await user.type(intervalInput, '7')

      await user.click(screen.getByText(/save expense/i))

      // Verify one-time expense was added
      await waitFor(() => {
        const store = useAppStore.getState()
        const oneTimeExpense = store.expenses.find(e => e.name === 'Car Purchase')
        expect(oneTimeExpense?.frequency).toBe('one-time')
        expect(oneTimeExpense?.yearsInterval).toBe(7)
      })
    })

    it('should update calculations when retirement parameters change', async () => {
      const user = userEvent.setup()
      
      render(
        <TestWrapper>
          <App />
        </TestWrapper>
      )

      // Change retirement duration
      const durationInput = screen.getByLabelText(/retirement duration/i)
      await user.clear(durationInput)
      await user.type(durationInput, '30')

      // Verify store was updated
      await waitFor(() => {
        const store = useAppStore.getState()
        expect(store.retirementDuration).toBe(30)
      })

      // Change annual return
      const returnInput = screen.getByLabelText(/annual return/i)
      await user.clear(returnInput)
      await user.type(returnInput, '15')

      // Verify calculations are recalculated
      await waitFor(() => {
        const store = useAppStore.getState()
        expect(store.retirementData.annualReturn).toBe(15)
      })
    })
  })

  describe('Error Handling', () => {
    it('should handle invalid input gracefully', async () => {
      const user = userEvent.setup()
      
      render(
        <TestWrapper>
          <App />
        </TestWrapper>
      )

      // Try to enter invalid values
      const currentAgeInput = screen.getByLabelText(/current age/i)
      await user.clear(currentAgeInput)
      await user.type(currentAgeInput, '-5')

      // Application should not crash
      expect(screen.getByText(/nest egg visionary/i)).toBeInTheDocument()
    })

    it('should handle edge case scenarios', async () => {
      const user = userEvent.setup()
      
      render(
        <TestWrapper>
          <App />
        </TestWrapper>
      )

      // Set retirement age before current age
      const currentAgeInput = screen.getByLabelText(/current age/i)
      await user.clear(currentAgeInput)
      await user.type(currentAgeInput, '70')

      const retirementAgeInput = screen.getByLabelText(/retirement age/i)
      await user.clear(retirementAgeInput)
      await user.type(retirementAgeInput, '65')

      // Application should handle this gracefully
      expect(screen.getByText(/nest egg visionary/i)).toBeInTheDocument()
    })
  })

  describe('Data Persistence', () => {
    it('should persist data across component remounts', () => {
      const { unmount } = render(
        <TestWrapper>
          <App />
        </TestWrapper>
      )

      // Modify some data
      const store = useAppStore.getState()
      store.updateRetirementData('currentAge', 40)
      store.setRetirementDuration(30)

      unmount()

      // Remount and check if data persists
      render(
        <TestWrapper>
          <App />
        </TestWrapper>
      )

      const newStore = useAppStore.getState()
      expect(newStore.retirementData.currentAge).toBe(40)
      expect(newStore.retirementDuration).toBe(30)
    })
  })

  describe('Accessibility', () => {
    it('should have proper ARIA labels and roles', () => {
      render(
        <TestWrapper>
          <App />
        </TestWrapper>
      )

      // Check for proper form labels
      expect(screen.getByLabelText(/current age/i)).toBeInTheDocument()
      expect(screen.getByLabelText(/retirement age/i)).toBeInTheDocument()

      // Check for proper button roles
      expect(screen.getByRole('button', { name: /add expense/i })).toBeInTheDocument()
    })

    it('should be keyboard navigable', async () => {
      const user = userEvent.setup()
      
      render(
        <TestWrapper>
          <App />
        </TestWrapper>
      )

      // Tab through form elements
      await user.tab()
      expect(document.activeElement).toHaveAttribute('type', 'number')

      await user.tab()
      expect(document.activeElement).toHaveAttribute('type', 'number')
    })
  })
})
