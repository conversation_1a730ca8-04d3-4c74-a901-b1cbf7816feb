import { describe, it, expect, beforeEach, vi } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import CostCalculation from '../CostCalculation'
import { useAppStore } from '@/stores/useAppStore'

// Mock the store
vi.mock('@/stores/useAppStore')

// Mock the calculation service
vi.mock('@/utils/calculationService', () => ({
  CalculationService: vi.fn().mockImplementation(() => ({
    calculateAll: vi.fn().mockReturnValue({
      costResults: {
        currentMonthlyCost: 50000,
        currentAnnualCost: 600000,
        adjustedMonthlyCost: 75000,
        adjustedAnnualCost: 900000,
        totalRetirementCost: 22500000,
        oneTimeCosts: 2500000,
        breakdown: {
          monthly: 18750000,
          yearly: 1250000,
          oneTime: 2500000,
        },
        detailedBreakdown: {
          currentYearlyCost: 100000,
          adjustedYearlyCost: 150000,
          yearlyExpensesByCategory: [],
          oneTimeExpensesByCategory: [],
        },
      },
    })),
  })),
}))

describe('CostCalculation', () => {
  const mockStore = {
    expenses: [
      {
        id: '1',
        name: 'Housing',
        iconName: 'Home',
        monthlyAmount: 25000,
        frequency: 'monthly' as const,
      },
      {
        id: '2',
        name: 'Insurance',
        iconName: 'Shield',
        monthlyAmount: 50000,
        frequency: 'yearly' as const,
      },
    ],
    retirementDuration: 25,
    showAddForm: false,
    newExpense: {
      name: '',
      iconName: 'DollarSign',
      monthlyAmount: 0,
      frequency: 'monthly' as const,
    },
    retirementData: {
      currentAge: 30,
      retirementAge: 60,
      inflationRate: 3,
    },
    updateExpense: vi.fn(),
    removeExpense: vi.fn(),
    setShowAddForm: vi.fn(),
    setRetirementDuration: vi.fn(),
    setCostResults: vi.fn(),
  }

  beforeEach(() => {
    vi.clearAllMocks()
    ;(useAppStore as any).mockReturnValue(mockStore)
  })

  it('should render cost calculation component', () => {
    render(<CostCalculation retirementAge={60} inflationRate={3} />)
    
    expect(screen.getByText('Cost Calculation')).toBeInTheDocument()
    expect(screen.getByText('Retirement Duration (Years)')).toBeInTheDocument()
  })

  it('should display expense cards', () => {
    render(<CostCalculation retirementAge={60} inflationRate={3} />)
    
    expect(screen.getByText('Housing')).toBeInTheDocument()
    expect(screen.getByText('Insurance')).toBeInTheDocument()
  })

  it('should handle retirement duration change', async () => {
    const user = userEvent.setup()
    render(<CostCalculation retirementAge={60} inflationRate={3} />)
    
    const durationInput = screen.getByDisplayValue('25')
    await user.clear(durationInput)
    await user.type(durationInput, '30')
    
    expect(mockStore.setRetirementDuration).toHaveBeenCalledWith(30)
  })

  it('should show add expense form when button is clicked', async () => {
    const user = userEvent.setup()
    render(<CostCalculation retirementAge={60} inflationRate={3} />)
    
    const addButton = screen.getByText('Add Expense')
    await user.click(addButton)
    
    expect(mockStore.setShowAddForm).toHaveBeenCalledWith(true)
  })

  it('should calculate and display cost results', () => {
    render(<CostCalculation retirementAge={60} inflationRate={3} />)
    
    // The component should trigger calculation and call setCostResults
    expect(mockStore.setCostResults).toHaveBeenCalled()
  })

  it('should handle empty expenses array', () => {
    const emptyStore = { ...mockStore, expenses: [] }
    ;(useAppStore as any).mockReturnValue(emptyStore)
    
    render(<CostCalculation retirementAge={60} inflationRate={3} />)
    
    expect(screen.getByText('Cost Calculation')).toBeInTheDocument()
    expect(screen.getByText('Add Expense')).toBeInTheDocument()
  })

  it('should validate retirement duration input', async () => {
    const user = userEvent.setup()
    render(<CostCalculation retirementAge={60} inflationRate={3} />)
    
    const durationInput = screen.getByDisplayValue('25')
    await user.clear(durationInput)
    await user.type(durationInput, '0')
    
    // Should handle edge case gracefully
    expect(mockStore.setRetirementDuration).toHaveBeenCalledWith(0)
  })

  it('should recalculate when expenses change', () => {
    const { rerender } = render(<CostCalculation retirementAge={60} inflationRate={3} />)
    
    const updatedStore = {
      ...mockStore,
      expenses: [
        ...mockStore.expenses,
        {
          id: '3',
          name: 'Food',
          iconName: 'Utensils',
          monthlyAmount: 15000,
          frequency: 'monthly' as const,
        },
      ],
    }
    ;(useAppStore as any).mockReturnValue(updatedStore)
    
    rerender(<CostCalculation retirementAge={60} inflationRate={3} />)
    
    // Should recalculate with new expenses
    expect(mockStore.setCostResults).toHaveBeenCalled()
  })

  it('should handle different retirement ages', () => {
    render(<CostCalculation retirementAge={65} inflationRate={3} />)
    
    expect(mockStore.setCostResults).toHaveBeenCalled()
  })

  it('should handle different inflation rates', () => {
    render(<CostCalculation retirementAge={60} inflationRate={5} />)
    
    expect(mockStore.setCostResults).toHaveBeenCalled()
  })

  describe('accessibility', () => {
    it('should have proper labels for form inputs', () => {
      render(<CostCalculation retirementAge={60} inflationRate={3} />)
      
      expect(screen.getByLabelText(/retirement duration/i)).toBeInTheDocument()
    })

    it('should have proper button roles', () => {
      render(<CostCalculation retirementAge={60} inflationRate={3} />)
      
      const addButton = screen.getByRole('button', { name: /add expense/i })
      expect(addButton).toBeInTheDocument()
    })
  })

  describe('error handling', () => {
    it('should handle calculation errors gracefully', () => {
      const errorStore = { ...mockStore }
      ;(useAppStore as any).mockReturnValue(errorStore)
      
      // Mock calculation service to throw error
      vi.mocked(require('@/utils/calculationService').CalculationService).mockImplementation(() => ({
        calculateAll: vi.fn().mockImplementation(() => {
          throw new Error('Calculation error')
        }),
      }))
      
      // Should not crash the component
      expect(() => {
        render(<CostCalculation retirementAge={60} inflationRate={3} />)
      }).not.toThrow()
    })

    it('should handle invalid input values', async () => {
      const user = userEvent.setup()
      render(<CostCalculation retirementAge={60} inflationRate={3} />)
      
      const durationInput = screen.getByDisplayValue('25')
      await user.clear(durationInput)
      await user.type(durationInput, '-5')
      
      // Should handle negative values
      expect(mockStore.setRetirementDuration).toHaveBeenCalledWith(-5)
    })
  })

  describe('performance', () => {
    it('should not recalculate unnecessarily', () => {
      const { rerender } = render(<CostCalculation retirementAge={60} inflationRate={3} />)
      
      const callCount = mockStore.setCostResults.mock.calls.length
      
      // Rerender with same props
      rerender(<CostCalculation retirementAge={60} inflationRate={3} />)
      
      // Should use memoization to avoid unnecessary recalculations
      expect(mockStore.setCostResults.mock.calls.length).toBe(callCount)
    })
  })
})
