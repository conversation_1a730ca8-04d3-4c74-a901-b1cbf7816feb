
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import ProjectionChart from './ProjectionChart';
import CostCalculation from './CostCalculation';
import { formatCurrency } from '@/utils/retirementCalculations';
import { Calculator, DollarSign, ShoppingCart } from 'lucide-react';
import { useRetirementCalculations } from '@/hooks/useRetirementCalculations';

const RetirementCalculator = () => {
  const { data, projections, handleInputChange } = useRetirementCalculations();

  return (
    <div className="max-w-7xl mx-auto space-y-6">
      <Tabs defaultValue="calculator" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="calculator" className="flex items-center gap-2">
            <Calculator className="w-4 h-4" />
            Calculator
          </TabsTrigger>
          <TabsTrigger value="projections" className="flex items-center gap-2">
            <DollarSign className="w-4 h-4" />
            Projections
          </TabsTrigger>
          <TabsTrigger value="costs" className="flex items-center gap-2">
            <ShoppingCart className="w-4 h-4" />
            Cost Analysis
          </TabsTrigger>
        </TabsList>

        <TabsContent value="calculator" className="space-y-6">
          <div className="grid md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Personal Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="currentAge">Current Age</Label>
                    <Input
                      id="currentAge"
                      type="number"
                      value={data.currentAge}
                      onChange={(e) => handleInputChange('currentAge', e.target.value)}
                      className="mt-1"
                    />
                  </div>
                  <div>
                    <Label htmlFor="retirementAge">Retirement Age</Label>
                    <Input
                      id="retirementAge"
                      type="number"
                      value={data.retirementAge}
                      onChange={(e) => handleInputChange('retirementAge', e.target.value)}
                      className="mt-1"
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Portfolio Details</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="currentPortfolio">Current Portfolio Value (₹)</Label>
                  <Input
                    id="currentPortfolio"
                    type="number"
                    value={data.currentPortfolio}
                    onChange={(e) => handleInputChange('currentPortfolio', e.target.value)}
                    className="mt-1"
                  />
                </div>
                <div>
                  <Label htmlFor="emergencyFund">Emergency Fund (₹)</Label>
                  <Input
                    id="emergencyFund"
                    type="number"
                    value={data.emergencyFund}
                    onChange={(e) => handleInputChange('emergencyFund', e.target.value)}
                    className="mt-1"
                  />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Monthly Contributions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="monthlyContribution">Current Monthly SIP (₹)</Label>
                  <Input
                    id="monthlyContribution"
                    type="number"
                    value={data.monthlyContribution}
                    onChange={(e) => handleInputChange('monthlyContribution', e.target.value)}
                    className="mt-1"
                  />
                </div>
                <div>
                  <Label htmlFor="contributionGrowthRate">Annual SIP Increase (%)</Label>
                  <Input
                    id="contributionGrowthRate"
                    type="number"
                    step="0.1"
                    value={data.contributionGrowthRate}
                    onChange={(e) => handleInputChange('contributionGrowthRate', e.target.value)}
                    className="mt-1"
                  />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Market Assumptions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="annualReturn">Expected Annual Return (%)</Label>
                  <Input
                    id="annualReturn"
                    type="number"
                    step="0.1"
                    value={data.annualReturn}
                    onChange={(e) => handleInputChange('annualReturn', e.target.value)}
                    className="mt-1"
                  />
                </div>
                <div>
                  <Label htmlFor="inflationRate">Expected Inflation Rate (%)</Label>
                  <Input
                    id="inflationRate"
                    type="number"
                    step="0.1"
                    value={data.inflationRate}
                    onChange={(e) => handleInputChange('inflationRate', e.target.value)}
                    className="mt-1"
                  />
                </div>
              </CardContent>
            </Card>
          </div>

          {projections && (
            <Card>
              <CardHeader>
                <CardTitle>Retirement Summary</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid md:grid-cols-4 gap-4 text-center">
                  <div className="p-4 bg-blue-50 rounded-lg">
                    <p className="text-sm text-gray-600">Years to Retirement</p>
                    <p className="text-2xl font-bold text-blue-600">{projections.yearsToRetirement}</p>
                  </div>
                  <div className="p-4 bg-green-50 rounded-lg">
                    <p className="text-sm text-gray-600">Final Portfolio Value</p>
                    <p className="text-2xl font-bold text-green-600">{formatCurrency(projections.finalValue)}</p>
                  </div>
                  <div className="p-4 bg-purple-50 rounded-lg">
                    <p className="text-sm text-gray-600">Real Value (Today's ₹)</p>
                    <p className="text-2xl font-bold text-purple-600">{formatCurrency(projections.realValue)}</p>
                  </div>
                  <div className="p-4 bg-orange-50 rounded-lg">
                    <p className="text-sm text-gray-600">Total Contributions</p>
                    <p className="text-2xl font-bold text-orange-600">{formatCurrency(projections.totalContributions)}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="projections">
          {projections && <ProjectionChart data={projections} retirementAge={data.retirementAge} />}
        </TabsContent>

        <TabsContent value="costs">
          <CostCalculation 
            retirementAge={data.retirementAge}
            inflationRate={data.inflationRate}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default RetirementCalculator;
