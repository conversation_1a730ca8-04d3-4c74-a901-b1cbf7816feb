import { <PERSON>, Card<PERSON>ontent, <PERSON>Header, CardTitle } from "@/components/ui/card";
import { TrendingUp } from "lucide-react";
import { formatCurrency } from "@/utils/retirementCalculations";
import { CostResults as CostResultsType } from "@/types/costCalculation";
import {
  getSafeDetailedBreakdown,
  hasDetailedBreakdown,
} from "@/utils/costDataHelpers";

interface CostResultsProps {
  results: CostResultsType;
  inflationRate: number;
  retirementDuration: number;
}

const CostResults = ({
  results,
  inflationRate,
  retirementDuration,
}: CostResultsProps) => {
  // Safe access to detailed breakdown with fallback defaults
  const safeDetailedBreakdown = getSafeDetailedBreakdown(results);
  const hasValidDetailedBreakdown = hasDetailedBreakdown(results);

  return (
    <div className="grid md:grid-cols-2 gap-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="w-5 h-5" />
            Current Cost Estimates
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="p-4 bg-blue-50 rounded-lg">
              <h3 className="font-bold text-lg mb-2">Monthly Expenses</h3>
              <p className="text-2xl font-bold text-blue-600">
                {formatCurrency(results.currentMonthlyCost)}
              </p>
            </div>
            <div className="p-4 bg-green-50 rounded-lg">
              <h3 className="font-bold text-lg mb-2">Annual Expenses</h3>
              <p className="text-2xl font-bold text-green-600">
                {formatCurrency(results.currentAnnualCost)}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Inflation-Adjusted Costs at Retirement</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="p-4 bg-orange-50 rounded-lg">
              <h3 className="font-bold text-lg mb-2">Monthly Expenses</h3>
              <p className="text-2xl font-bold text-orange-600">
                {formatCurrency(results.adjustedMonthlyCost)}
              </p>
              <p className="text-sm text-orange-700">
                At {inflationRate}% inflation
              </p>
            </div>
            <div className="p-4 bg-purple-50 rounded-lg">
              <h3 className="font-bold text-lg mb-2">Annual Expenses</h3>
              <p className="text-2xl font-bold text-purple-600">
                {formatCurrency(results.adjustedAnnualCost)}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card className="md:col-span-2">
        <CardHeader>
          <CardTitle>Total Retirement Cost Analysis</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid md:grid-cols-3 gap-4">
            <div className="p-4 bg-red-50 rounded-lg text-center">
              <h3 className="font-bold text-lg mb-2">Total Cost</h3>
              <p className="text-3xl font-bold text-red-600">
                {formatCurrency(results.totalRetirementCost)}
              </p>
              <p className="text-sm text-red-700 mt-1">
                For {retirementDuration} years
              </p>
            </div>
            <div className="p-4 bg-yellow-50 rounded-lg">
              <h3 className="font-bold mb-2">Cost Breakdown</h3>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>Monthly Expenses:</span>
                  <span className="font-semibold">
                    {formatCurrency(results.breakdown.monthly)}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>Yearly Expenses:</span>
                  <span className="font-semibold">
                    {formatCurrency(results.breakdown.yearly)}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>One-time Costs:</span>
                  <span className="font-semibold">
                    {formatCurrency(results.breakdown.oneTime)}
                  </span>
                </div>
              </div>
            </div>
            <div className="p-4 bg-gray-50 rounded-lg">
              <h3 className="font-bold mb-2">Key Insights</h3>
              <div className="space-y-1 text-sm text-gray-700">
                <p>
                  • Plan for {formatCurrency(results.adjustedMonthlyCost)}{" "}
                  monthly
                </p>
                <p>
                  • Yearly expenses:{" "}
                  {formatCurrency(safeDetailedBreakdown.adjustedYearlyCost)}
                </p>
                <p>• Major purchases: {formatCurrency(results.oneTimeCosts)}</p>
                <p>
                  • Inflation impact:{" "}
                  {(
                    (results.adjustedMonthlyCost / results.currentMonthlyCost -
                      1) *
                    100
                  ).toFixed(1)}
                  %
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Detailed Yearly Expenses Breakdown */}
      {hasValidDetailedBreakdown &&
        safeDetailedBreakdown.yearlyExpensesByCategory.length > 0 && (
          <Card className="md:col-span-2">
            <CardHeader>
              <CardTitle>Yearly Expenses Breakdown</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
                {safeDetailedBreakdown.yearlyExpensesByCategory.map(
                  (expense, index) => (
                    <div key={index} className="p-4 bg-green-50 rounded-lg">
                      <h4 className="font-semibold text-green-800 mb-2">
                        {expense.name}
                      </h4>
                      <div className="space-y-1 text-sm">
                        <div className="flex justify-between">
                          <span>Current:</span>
                          <span className="font-medium">
                            {formatCurrency(expense.currentAmount)}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span>At Retirement:</span>
                          <span className="font-medium text-green-600">
                            {formatCurrency(expense.adjustedAmount)}
                          </span>
                        </div>
                        <div className="flex justify-between text-xs text-green-700">
                          <span>Total over {retirementDuration} years:</span>
                          <span className="font-medium">
                            {formatCurrency(
                              expense.adjustedAmount * retirementDuration
                            )}
                          </span>
                        </div>
                      </div>
                    </div>
                  )
                )}
              </div>
            </CardContent>
          </Card>
        )}

      {/* Detailed One-Time Expenses Breakdown */}
      {hasValidDetailedBreakdown &&
        safeDetailedBreakdown.oneTimeExpensesByCategory.length > 0 && (
          <Card className="md:col-span-2">
            <CardHeader>
              <CardTitle>One-Time & Fixed Expenses Breakdown</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
                {safeDetailedBreakdown.oneTimeExpensesByCategory.map(
                  (expense, index) => (
                    <div key={index} className="p-4 bg-purple-50 rounded-lg">
                      <h4 className="font-semibold text-purple-800 mb-2">
                        {expense.name}
                      </h4>
                      <div className="space-y-1 text-sm">
                        <div className="flex justify-between">
                          <span>Cost per occurrence:</span>
                          <span className="font-medium">
                            {formatCurrency(expense.costPerOccurrence)}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span>Every {expense.yearsInterval} years</span>
                          <span className="font-medium">
                            {expense.totalOccurrences} times
                          </span>
                        </div>
                        <div className="flex justify-between text-purple-700">
                          <span>Total cost:</span>
                          <span className="font-bold text-purple-600">
                            {formatCurrency(expense.totalCost)}
                          </span>
                        </div>
                        <div className="text-xs text-purple-600 mt-2">
                          Includes inflation adjustment for each occurrence
                        </div>
                      </div>
                    </div>
                  )
                )}
              </div>
            </CardContent>
          </Card>
        )}
    </div>
  );
};

export default CostResults;
