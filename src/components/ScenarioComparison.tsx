
import { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { calculateScenario, formatCurrency } from '@/utils/retirementCalculations';

interface ScenarioComparisonProps {
  baseData: any;
}

const ScenarioComparison = ({ baseData }: ScenarioComparisonProps) => {
  const [scenarios, setScenarios] = useState([
    {
      name: 'Conservative',
      modifications: { annualReturn: 8, contributionGrowthRate: 5 },
      result: null as any,
    },
    {
      name: 'Aggressive',
      modifications: { annualReturn: 15, contributionGrowthRate: 10 },
      result: null as any,
    },
    {
      name: 'Market Downturn',
      modifications: { annualReturn: 6, monthlyContribution: baseData.monthlyContribution * 0.8 },
      result: null as any,
    },
  ]);

  const [customScenario, setCustomScenario] = useState({
    name: 'Custom',
    annualReturn: baseData.annualReturn,
    monthlyContribution: baseData.monthlyContribution,
    contributionGrowthRate: baseData.contributionGrowthRate,
  });

  const calculateAllScenarios = () => {
    const baseResult = calculateScenario(baseData, {});
    
    const updatedScenarios = scenarios.map(scenario => ({
      ...scenario,
      result: calculateScenario(baseData, scenario.modifications),
    }));

    setScenarios(updatedScenarios);
    console.log('Base scenario result:', baseResult);
    console.log('All scenarios calculated:', updatedScenarios);
  };

  const addCustomScenario = () => {
    const customResult = calculateScenario(baseData, {
      annualReturn: customScenario.annualReturn,
      monthlyContribution: customScenario.monthlyContribution,
      contributionGrowthRate: customScenario.contributionGrowthRate,
    });

    setScenarios(prev => [...prev, {
      name: customScenario.name,
      modifications: {
        annualReturn: customScenario.annualReturn,
        monthlyContribution: customScenario.monthlyContribution,
        contributionGrowthRate: customScenario.contributionGrowthRate,
      },
      result: customResult,
    }]);
  };

  const baseResult = calculateScenario(baseData, {});

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Scenario Planning</CardTitle>
        </CardHeader>
        <CardContent>
          <Button onClick={calculateAllScenarios} className="mb-4">
            Calculate All Scenarios
          </Button>
          
          <div className="grid gap-4">
            <div className="p-4 border-2 border-blue-200 rounded-lg bg-blue-50">
              <h3 className="font-bold text-lg mb-2">Base Scenario</h3>
              <div className="grid md:grid-cols-3 gap-4 text-sm">
                <div>
                  <span className="text-gray-600">Final Value:</span>
                  <p className="font-bold">{formatCurrency(baseResult.finalValue)}</p>
                </div>
                <div>
                  <span className="text-gray-600">Real Value:</span>
                  <p className="font-bold">{formatCurrency(baseResult.realValue)}</p>
                </div>
                <div>
                  <span className="text-gray-600">Monthly Income:</span>
                  <p className="font-bold">{formatCurrency(baseResult.monthlyRetirementIncome)}</p>
                </div>
              </div>
            </div>

            {scenarios.map((scenario, index) => (
              scenario.result && (
                <div key={index} className="p-4 border rounded-lg">
                  <h3 className="font-bold text-lg mb-2">{scenario.name} Scenario</h3>
                  <div className="grid md:grid-cols-3 gap-4 text-sm">
                    <div>
                      <span className="text-gray-600">Final Value:</span>
                      <p className="font-bold">{formatCurrency(scenario.result.finalValue)}</p>
                      <p className={`text-xs ${scenario.result.finalValue > baseResult.finalValue ? 'text-green-600' : 'text-red-600'}`}>
                        {scenario.result.finalValue > baseResult.finalValue ? '+' : ''}
                        {formatCurrency(scenario.result.finalValue - baseResult.finalValue)}
                      </p>
                    </div>
                    <div>
                      <span className="text-gray-600">Real Value:</span>
                      <p className="font-bold">{formatCurrency(scenario.result.realValue)}</p>
                      <p className={`text-xs ${scenario.result.realValue > baseResult.realValue ? 'text-green-600' : 'text-red-600'}`}>
                        {scenario.result.realValue > baseResult.realValue ? '+' : ''}
                        {formatCurrency(scenario.result.realValue - baseResult.realValue)}
                      </p>
                    </div>
                    <div>
                      <span className="text-gray-600">Monthly Income:</span>
                      <p className="font-bold">{formatCurrency(scenario.result.monthlyRetirementIncome)}</p>
                      <p className={`text-xs ${scenario.result.monthlyRetirementIncome > baseResult.monthlyRetirementIncome ? 'text-green-600' : 'text-red-600'}`}>
                        {scenario.result.monthlyRetirementIncome > baseResult.monthlyRetirementIncome ? '+' : ''}
                        {formatCurrency(scenario.result.monthlyRetirementIncome - baseResult.monthlyRetirementIncome)}
                      </p>
                    </div>
                  </div>
                </div>
              )
            ))}
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Create Custom Scenario</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid md:grid-cols-2 gap-4 mb-4">
            <div>
              <Label htmlFor="customName">Scenario Name</Label>
              <Input
                id="customName"
                value={customScenario.name}
                onChange={(e) => setCustomScenario(prev => ({ ...prev, name: e.target.value }))}
                className="mt-1"
              />
            </div>
            <div>
              <Label htmlFor="customReturn">Annual Return (%)</Label>
              <Input
                id="customReturn"
                type="number"
                step="0.1"
                value={customScenario.annualReturn}
                onChange={(e) => setCustomScenario(prev => ({ ...prev, annualReturn: parseFloat(e.target.value) || 0 }))}
                className="mt-1"
              />
            </div>
            <div>
              <Label htmlFor="customContribution">Monthly SIP (₹)</Label>
              <Input
                id="customContribution"
                type="number"
                value={customScenario.monthlyContribution}
                onChange={(e) => setCustomScenario(prev => ({ ...prev, monthlyContribution: parseFloat(e.target.value) || 0 }))}
                className="mt-1"
              />
            </div>
            <div>
              <Label htmlFor="customGrowth">SIP Growth Rate (%)</Label>
              <Input
                id="customGrowth"
                type="number"
                step="0.1"
                value={customScenario.contributionGrowthRate}
                onChange={(e) => setCustomScenario(prev => ({ ...prev, contributionGrowthRate: parseFloat(e.target.value) || 0 }))}
                className="mt-1"
              />
            </div>
          </div>
          <Button onClick={addCustomScenario}>Add Custom Scenario</Button>
        </CardContent>
      </Card>
    </div>
  );
};

export default ScenarioComparison;
