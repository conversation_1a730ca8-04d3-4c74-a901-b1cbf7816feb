import { useMemo } from "react";
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  AreaChart,
  Area,
  Scatter,
  ReferenceDot,
} from "recharts";
import { formatCurrency } from "@/utils/retirementCalculations";
import {
  AXIS_LABELS,
  SERIES_KEYS,
  getOneTimeCostDetailsForAge,
  toChartData,
  getFundDepletionAge,
  getYearlyExpensesForAge,
} from "@/utils/chart";
import { useAppStore } from "@/stores/useAppStore";
import {
  CalculationService,
  ProjectionPoint,
  ProjectionInputData,
} from "@/utils/calculationService";
import {
  getAnnualCoveragePct,
  getFundDuration,
  getNetMonthly,
  getReturnMultiple,
  getTotalReturn,
} from "@/utils/kpis";
import {
  getSafeDetailedBreakdown,
  getSafeCostValue,
  hasDetailedBreakdown,
} from "@/utils/costDataHelpers";
import { INFLATION_RATE } from "@/constants/finance";

interface ProjectionChartProps {
  data: ProjectionInputData;
  retirementAge: number;
}

const ProjectionChart = ({ data, retirementAge }: ProjectionChartProps) => {
  const {
    costResults: costAnalysisData,
    retirementDuration,
    expenses,
    retirementData,
  } = useAppStore();

  // Use the calculation service directly instead of hooks
  const calculationService = useMemo(() => {
    if (!expenses || expenses.length === 0) return null;
    return new CalculationService(
      expenses,
      retirementAge,
      retirementDuration,
      retirementData.currentAge,
      INFLATION_RATE * 100, // Convert decimal to percentage for CalculationService
      retirementData.annualReturn
    );
  }, [
    expenses,
    retirementAge,
    retirementDuration,
    retirementData.currentAge,
    retirementData.annualReturn,
  ]);

  const enhancedChartData = useMemo(() => {
    if (!calculationService) return data.yearlyProjections;

    const { costResults, oneTimeOccurrences } =
      calculationService.calculateAll();
    return calculationService.generateEnhancedProjections(
      data,
      oneTimeOccurrences,
      costResults.adjustedAnnualCost
    );
  }, [calculationService, data]);

  const oneTimeCostOccurrences = useMemo(() => {
    if (!calculationService) return [];
    return calculationService.calculateAll().oneTimeOccurrences;
  }, [calculationService]);

  const safeDetailedBreakdown = useMemo(() => {
    return getSafeDetailedBreakdown(costAnalysisData);
  }, [costAnalysisData]);

  const hasValidDetailedBreakdown = useMemo(() => {
    return hasDetailedBreakdown(costAnalysisData);
  }, [costAnalysisData]);

  const chartData = useMemo(
    () => toChartData(enhancedChartData),
    [enhancedChartData]
  );
  const fundDepletionAge = useMemo(
    () => getFundDepletionAge(chartData),
    [chartData]
  );

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>
            Portfolio Growth & Retirement Usage with One-Time Expenses
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-96">
            <ResponsiveContainer width="100%" height="100%">
              <AreaChart data={chartData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis
                  dataKey="age"
                  label={{
                    value: AXIS_LABELS.xAge,
                    position: "insideBottom",
                    offset: -5,
                  }}
                />
                <YAxis
                  tickFormatter={formatCurrency}
                  label={{
                    value: AXIS_LABELS.yValueCurrency,
                    angle: -90,
                    position: "insideLeft",
                  }}
                />
                <Tooltip
                  formatter={(value: number, name: string) => [
                    formatCurrency(value),
                    name,
                  ]}
                  labelFormatter={(age) => `Age: ${age}`}
                  content={({ active, payload, label }) => {
                    if (active && payload && payload.length) {
                      const chartDataPoint = payload[0].payload;
                      const oneTimeCosts = chartDataPoint["One-Time Costs"];
                      const annualCosts = chartDataPoint["Annual Costs"];
                      const portfolioValue = chartDataPoint["Portfolio Value"];
                      const oneTimeCostDetails = getOneTimeCostDetailsForAge(
                        oneTimeCostOccurrences,
                        chartDataPoint.age
                      );

                      // Use pre-calculated values from the enhanced projection data
                      const enhancedPoint = enhancedChartData.find(
                        (point) => point.age === chartDataPoint.age
                      );
                      const incomeFromInterest =
                        enhancedPoint?.incomeFromInterest || 0;
                      const consolidatedCosts =
                        enhancedPoint?.consolidatedCosts || 0;
                      const netPosition = enhancedPoint?.netPosition || 0;

                      return (
                        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
                          <p className="font-semibold">{`Age: ${label}`}</p>
                          {payload.map((entry, index) => (
                            <p key={index} style={{ color: entry.color }}>
                              {`${entry.name}: ${formatCurrency(
                                entry.value as number
                              )}`}
                            </p>
                          ))}

                          {/* Use pre-calculated consolidated data */}
                          {chartDataPoint.phase === "Retirement" &&
                            enhancedPoint && (
                              <div className="mt-2 pt-2 border-t border-gray-200">
                                <div className="mb-2">
                                  <p className="font-semibold text-green-600">
                                    Income from Interest:
                                  </p>
                                  <p className="text-sm text-green-700">
                                    {formatCurrency(incomeFromInterest)}
                                  </p>
                                </div>
                                <div className="mb-2">
                                  <p className="font-semibold text-red-600">
                                    Consolidated Costs:
                                  </p>
                                  <p className="text-sm text-red-700">
                                    {formatCurrency(consolidatedCosts)}
                                  </p>
                                </div>
                                <div className="pt-1 border-t border-gray-100">
                                  <p className="font-semibold text-gray-800">
                                    Net Position:
                                  </p>
                                  <p
                                    className={`text-sm ${
                                      netPosition >= 0
                                        ? "text-green-700"
                                        : "text-red-700"
                                    }`}
                                  >
                                    {formatCurrency(netPosition)}
                                  </p>
                                </div>
                              </div>
                            )}

                          {oneTimeCosts > 0 && (
                            <div className="mt-2 pt-2 border-t border-gray-200">
                              <p className="font-semibold text-purple-600">
                                One-Time Expenses:
                              </p>
                              {oneTimeCostDetails.map((detail, index) => (
                                <p
                                  key={index}
                                  className="text-sm text-purple-700"
                                >
                                  • {detail.expenseName}:{" "}
                                  {formatCurrency(detail.amount)}
                                </p>
                              ))}
                            </div>
                          )}
                        </div>
                      );
                    }
                    return null;
                  }}
                />
                <Legend />
                <Area
                  type="monotone"
                  dataKey={SERIES_KEYS.portfolioValue}
                  stackId="1"
                  stroke="#2563eb"
                  fill="#3b82f6"
                  fillOpacity={0.3}
                />
                <Line
                  type="monotone"
                  dataKey={SERIES_KEYS.realValue}
                  stroke="#dc2626"
                  strokeWidth={2}
                  strokeDasharray="5 5"
                  dot={false}
                />
                {costAnalysisData && (
                  <Line
                    type="monotone"
                    dataKey={SERIES_KEYS.annualCosts}
                    stroke="#f59e0b"
                    strokeWidth={2}
                    dot={false}
                  />
                )}
                {/* One-time cost markers */}
                {costAnalysisData && hasValidDetailedBreakdown && (
                  <Line
                    type="monotone"
                    dataKey={SERIES_KEYS.oneTimeCosts}
                    stroke="#8b5cf6"
                    strokeWidth={0}
                    dot={{
                      fill: "#8b5cf6",
                      stroke: "#ffffff",
                      strokeWidth: 2,
                      r: 6,
                    }}
                    connectNulls={false}
                  />
                )}
              </AreaChart>
            </ResponsiveContainer>
          </div>
          {fundDepletionAge && (
            <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
              <p className="text-red-700 font-medium">
                ⚠️ Warning: Portfolio may be depleted by age {fundDepletionAge}
              </p>
            </div>
          )}

          {/* Chart Legend and One-Time Cost Summary */}
          {costAnalysisData &&
            hasValidDetailedBreakdown &&
            oneTimeCostOccurrences.length > 0 && (
              <div className="mt-4 p-4 bg-purple-50 border border-purple-200 rounded-lg">
                <h4 className="font-semibold text-purple-800 mb-2 flex items-center">
                  <span className="w-3 h-3 bg-purple-500 rounded-full mr-2"></span>
                  One-Time Expense Schedule
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                  {oneTimeCostOccurrences.map((occurrence, index) => (
                    <div key={index} className="text-sm">
                      <span className="font-medium">Age {occurrence.age}:</span>{" "}
                      <span className="text-purple-700">
                        {occurrence.expenseName}
                      </span>{" "}
                      <span className="font-semibold">
                        {formatCurrency(occurrence.amount)}
                      </span>
                    </div>
                  ))}
                </div>
                <p className="text-xs text-purple-600 mt-2">
                  💡 Purple dots on the chart mark when these expenses occur.
                  Hover over them for details.
                </p>
              </div>
            )}
        </CardContent>
      </Card>

      <div className="grid md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Retirement Income Projection</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex justify-between items-center p-3 bg-blue-50 rounded-lg">
                <span className="font-medium">Monthly Income (4% Rule)</span>
                <span className="text-xl font-bold text-blue-600">
                  {formatCurrency(data.monthlyRetirementIncome)}
                </span>
              </div>
              <div className="flex justify-between items-center p-3 bg-green-50 rounded-lg">
                <span className="font-medium">
                  Real Monthly Income (Today's Value)
                </span>
                <span className="text-xl font-bold text-green-600">
                  {formatCurrency(data.realMonthlyIncome)}
                </span>
              </div>
              {costAnalysisData && (
                <>
                  <div className="flex justify-between items-center p-3 bg-orange-50 rounded-lg">
                    <span className="font-medium">
                      Projected Monthly Expenses
                    </span>
                    <span className="text-xl font-bold text-orange-600">
                      {formatCurrency(costAnalysisData.adjustedMonthlyCost)}
                    </span>
                  </div>
                  <div className="flex justify-between items-center p-3 bg-yellow-50 rounded-lg">
                    <span className="font-medium">
                      Projected Yearly Expenses
                    </span>
                    <span className="text-xl font-bold text-yellow-600">
                      {formatCurrency(safeDetailedBreakdown.adjustedYearlyCost)}
                    </span>
                  </div>
                </>
              )}
              <p className="text-sm text-gray-600 mt-2">
                * Based on the 4% withdrawal rule, which suggests withdrawing 4%
                of your portfolio value annually in retirement.
              </p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Fund Sustainability Analysis</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span>Total Return:</span>
                <span className="font-bold">
                  {formatCurrency(
                    getTotalReturn(data.finalValue, data.totalContributions)
                  )}
                </span>
              </div>
              <div className="flex justify-between">
                <span>Return Multiple:</span>
                <span className="font-bold">
                  {getReturnMultiple(
                    data.finalValue,
                    data.totalContributions
                  ).toFixed(1)}
                  x
                </span>
              </div>
              {costAnalysisData && (
                <>
                  <div className="flex justify-between">
                    <span>Annual Expense Coverage:</span>
                    <span className="font-bold">
                      {getAnnualCoveragePct(
                        data.monthlyRetirementIncome,
                        costAnalysisData.adjustedAnnualCost
                      ).toFixed(0)}
                      %
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>Fund Duration:</span>
                    <span className="font-bold">
                      {getFundDuration(
                        fundDepletionAge,
                        retirementAge,
                        retirementDuration
                      )}
                    </span>
                  </div>
                </>
              )}
            </div>
            {costAnalysisData && (
              <div className="mt-4 p-3 bg-gray-50 rounded-lg">
                <h4 className="font-semibold mb-2">
                  Income vs Expenses Breakdown
                </h4>
                <div className="space-y-1 text-sm">
                  <div className="flex justify-between">
                    <span>Monthly Income:</span>
                    <span className="text-green-600">
                      {formatCurrency(data.monthlyRetirementIncome)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>Monthly Expenses:</span>
                    <span className="text-red-600">
                      {formatCurrency(costAnalysisData.adjustedMonthlyCost)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>Yearly Expenses:</span>
                    <span className="text-orange-600">
                      {formatCurrency(safeDetailedBreakdown.adjustedYearlyCost)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>One-time Costs (Total):</span>
                    <span className="text-purple-600">
                      {formatCurrency(costAnalysisData.oneTimeCosts)}
                    </span>
                  </div>
                  <hr className="my-1" />
                  <div className="flex justify-between font-semibold">
                    <span>Net Monthly:</span>
                    <span
                      className={
                        data.monthlyRetirementIncome -
                          costAnalysisData.adjustedMonthlyCost >=
                        0
                          ? "text-green-600"
                          : "text-red-600"
                      }
                    >
                      {formatCurrency(
                        getNetMonthly(
                          data.monthlyRetirementIncome,
                          costAnalysisData.adjustedMonthlyCost
                        )
                      )}
                    </span>
                  </div>
                  <div className="flex justify-between font-semibold">
                    <span>Annual Coverage Ratio:</span>
                    <span
                      className={
                        data.monthlyRetirementIncome * 12 >=
                        costAnalysisData.adjustedAnnualCost
                          ? "text-green-600"
                          : "text-red-600"
                      }
                    >
                      {getAnnualCoveragePct(
                        data.monthlyRetirementIncome,
                        costAnalysisData.adjustedAnnualCost
                      ).toFixed(0)}
                      %
                    </span>
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Detailed Expense Projections */}
      {costAnalysisData &&
        hasValidDetailedBreakdown &&
        (safeDetailedBreakdown.yearlyExpensesByCategory.length > 0 ||
          safeDetailedBreakdown.oneTimeExpensesByCategory.length > 0) && (
          <div className="grid md:grid-cols-2 gap-6">
            {/* Yearly Expenses Projection */}
            {safeDetailedBreakdown.yearlyExpensesByCategory.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle>Yearly Expenses in Retirement</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {safeDetailedBreakdown.yearlyExpensesByCategory.map(
                      (expense, index) => (
                        <div
                          key={index}
                          className="p-3 bg-yellow-50 rounded-lg"
                        >
                          <div className="flex justify-between items-center">
                            <span className="font-medium">{expense.name}</span>
                            <span className="text-lg font-bold text-yellow-600">
                              {formatCurrency(expense.adjustedAmount)}
                            </span>
                          </div>
                          <div className="text-xs text-yellow-700 mt-1">
                            Current: {formatCurrency(expense.currentAmount)} |
                            Total over {Math.max(0, retirementDuration || 0)}{" "}
                            years:{" "}
                            {formatCurrency(
                              expense.adjustedAmount *
                                Math.max(0, retirementDuration || 0)
                            )}
                          </div>
                        </div>
                      )
                    )}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* One-Time Expenses Projection */}
            {safeDetailedBreakdown.oneTimeExpensesByCategory.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle>Fixed & One-Time Expenses</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {safeDetailedBreakdown.oneTimeExpensesByCategory.map(
                      (expense, index) => (
                        <div
                          key={index}
                          className="p-3 bg-purple-50 rounded-lg"
                        >
                          <div className="flex justify-between items-center">
                            <span className="font-medium">{expense.name}</span>
                            <span className="text-lg font-bold text-purple-600">
                              {formatCurrency(expense.totalCost)}
                            </span>
                          </div>
                          <div className="text-xs text-purple-700 mt-1">
                            {formatCurrency(expense.costPerOccurrence)} every{" "}
                            {expense.yearsInterval} years |
                            {expense.totalOccurrences} occurrences total
                          </div>
                        </div>
                      )
                    )}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        )}

      {!costAnalysisData && (
        <Card>
          <CardContent className="p-6">
            <div className="text-center text-gray-500">
              <p className="mb-2">
                💡 Complete the Cost Analysis tab to see detailed retirement
                expense projections here.
              </p>
              <p className="text-sm">
                This will show you how your portfolio will be used during
                retirement and whether your savings will last.
              </p>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default ProjectionChart;
