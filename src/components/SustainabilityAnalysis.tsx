
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { calculateSustainability, calculateInvestmentRecommendation, formatCurrency } from '@/utils/retirementCalculations';
import { AlertTriangle, CheckCircle, TrendingUp, Target } from 'lucide-react';

interface SustainabilityAnalysisProps {
  portfolioValue: number;
  retirementAge: number;
  currentAge: number;
  currentPortfolio: number;
  inflationRate: number;
  sustainabilityInputs: {
    monthlyExpenses: number;
    lifeExpectancy: number;
    postRetirementReturn: number;
  };
  setSustainabilityInputs: (inputs: any) => void;
  sustainabilityResults: any;
  setSustainabilityResults: (results: any) => void;
  sustainabilityRecommendation: any;
  setSustainabilityRecommendation: (recommendation: any) => void;
}

const SustainabilityAnalysis = ({ 
  portfolioValue, 
  retirementAge, 
  currentAge, 
  currentPortfolio, 
  inflationRate,
  sustainabilityInputs,
  setSustainabilityInputs,
  sustainabilityResults,
  setSustainabilityResults,
  sustainabilityRecommendation,
  setSustainabilityRecommendation
}: SustainabilityAnalysisProps) => {

  const handleInputChange = (field: string, value: number) => {
    setSustainabilityInputs(prev => ({ ...prev, [field]: value }));
  };

  const analyzesustainability = () => {
    const sustainabilityResult = calculateSustainability(
      portfolioValue,
      sustainabilityInputs.monthlyExpenses,
      retirementAge,
      sustainabilityInputs.lifeExpectancy,
      inflationRate,
      sustainabilityInputs.postRetirementReturn
    );

    const recommendationResult = calculateInvestmentRecommendation(
      currentAge,
      retirementAge,
      currentPortfolio,
      sustainabilityInputs.monthlyExpenses,
      sustainabilityInputs.lifeExpectancy,
      inflationRate,
      12, // pre-retirement return
      sustainabilityInputs.postRetirementReturn
    );

    setSustainabilityResults(sustainabilityResult);
    setSustainabilityRecommendation(recommendationResult);
    console.log('Sustainability analysis:', sustainabilityResult);
    console.log('Investment recommendation:', recommendationResult);
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Target className="w-5 h-5" />
            Retirement Sustainability Analysis
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="monthlyExpenses">Expected Monthly Expenses in Retirement (₹)</Label>
              <Input
                id="monthlyExpenses"
                type="number"
                value={sustainabilityInputs.monthlyExpenses}
                onChange={(e) => handleInputChange('monthlyExpenses', parseFloat(e.target.value) || 0)}
                className="mt-1"
              />
            </div>
            <div>
              <Label htmlFor="lifeExpectancy">Life Expectancy</Label>
              <Input
                id="lifeExpectancy"
                type="number"
                value={sustainabilityInputs.lifeExpectancy}
                onChange={(e) => handleInputChange('lifeExpectancy', parseFloat(e.target.value) || 0)}
                className="mt-1"
              />
            </div>
            <div>
              <Label htmlFor="postRetirementReturn">Post-Retirement Investment Return (%)</Label>
              <Input
                id="postRetirementReturn"
                type="number"
                step="0.1"
                value={sustainabilityInputs.postRetirementReturn}
                onChange={(e) => handleInputChange('postRetirementReturn', parseFloat(e.target.value) || 0)}
                className="mt-1"
              />
            </div>
          </div>
          <Button onClick={analyzesustainability} className="w-full">
            Analyze Sustainability
          </Button>
        </CardContent>
      </Card>

      {sustainabilityResults && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              {sustainabilityResults.issustainable ? (
                <CheckCircle className="w-5 h-5 text-green-600" />
              ) : (
                <AlertTriangle className="w-5 h-5 text-red-600" />
              )}
              Sustainability Results
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-2 gap-4">
              <div className={`p-4 rounded-lg ${sustainabilityResults.issustainable ? 'bg-green-50' : 'bg-red-50'}`}>
                <h3 className="font-bold text-lg mb-2">
                  {sustainabilityResults.issustainable ? 'Funds Will Last' : 'Funds Will Deplete'}
                </h3>
                {sustainabilityResults.issustainable ? (
                  <p className="text-green-700">
                    Your retirement corpus is sufficient to last your entire retirement period.
                  </p>
                ) : (
                  <div className="text-red-700">
                    <p>Funds will run out in <strong>{sustainabilityResults.yearsUntilDepletion?.toFixed(1)} years</strong></p>
                    <p>At age <strong>{sustainabilityResults.ageWhenDepleted?.toFixed(0)}</strong></p>
                  </div>
                )}
              </div>
              
              <div className="p-4 bg-blue-50 rounded-lg">
                <h3 className="font-bold text-lg mb-2">Monthly Withdrawal</h3>
                <p className="text-blue-700">
                  <strong>{formatCurrency(sustainabilityResults.monthlyWithdrawalAmount)}</strong>
                </p>
                <p className="text-sm text-blue-600">
                  Inflation-adjusted expenses
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {sustainabilityRecommendation && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="w-5 h-5" />
              Investment Recommendations
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-2 gap-4">
              <div className="p-4 bg-orange-50 rounded-lg">
                <h3 className="font-bold text-lg mb-2">Target Corpus Required</h3>
                <p className="text-2xl font-bold text-orange-600">
                  {formatCurrency(sustainabilityRecommendation.targetCorpus)}
                </p>
                <p className="text-sm text-orange-700 mt-1">
                  To sustain ₹{sustainabilityInputs.monthlyExpenses.toLocaleString()} monthly expenses
                </p>
              </div>
              
              <div className="p-4 bg-purple-50 rounded-lg">
                <h3 className="font-bold text-lg mb-2">Recommended Monthly SIP</h3>
                <p className="text-2xl font-bold text-purple-600">
                  {formatCurrency(sustainabilityRecommendation.recommendedMonthlyInvestment)}
                </p>
                <p className="text-sm text-purple-700 mt-1">
                  To meet your retirement goals
                </p>
              </div>
              
              {sustainabilityRecommendation.currentShortfall > 0 && (
                <div className="p-4 bg-yellow-50 rounded-lg">
                  <h3 className="font-bold text-lg mb-2">Current Shortfall</h3>
                  <p className="text-2xl font-bold text-yellow-600">
                    {formatCurrency(sustainabilityRecommendation.currentShortfall)}
                  </p>
                  <p className="text-sm text-yellow-700 mt-1">
                    Additional corpus needed
                  </p>
                </div>
              )}
            </div>
            
            {sustainabilityRecommendation.currentShortfall > 0 && (
              <div className="mt-4 p-4 border-l-4 border-orange-500 bg-orange-50">
                <h4 className="font-bold text-orange-800">💡 Recommendation</h4>
                <p className="text-orange-700 mt-1">
                  Consider increasing your monthly SIP to <strong>{formatCurrency(sustainabilityRecommendation.recommendedMonthlyInvestment)}</strong> 
                  to ensure a comfortable retirement with your desired lifestyle.
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default SustainabilityAnalysis;
