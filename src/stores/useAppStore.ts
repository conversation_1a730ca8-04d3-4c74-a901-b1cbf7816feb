import { create } from "zustand";
import { persist } from "zustand/middleware";
import { ExpenseCategory, CostResults } from "@/types/costCalculation";
import { defaultExpenses } from "@/constants/defaultExpenses";
import {
  migrateCostResults,
  isValidCostResults,
} from "@/utils/costDataHelpers";

// Retirement data interface
export interface RetirementData {
  currentAge: number;
  retirementAge: number;
  currentPortfolio: number;
  monthlyContribution: number;
  annualReturn: number;
  inflationRate: number;
  contributionGrowthRate: number;
  emergencyFund: number;
}

// Unified app store interface
interface AppStore {
  // Cost calculation state
  expenses: ExpenseCategory[];
  retirementDuration: number;
  costResults: CostResults | null;
  showAddForm: boolean;
  newExpense: Partial<ExpenseCategory>;

  // Retirement data state
  retirementData: RetirementData;

  // Cost calculation actions
  setExpenses: (expenses: ExpenseCategory[]) => void;
  updateExpense: (id: string, field: keyof ExpenseCategory, value: any) => void;
  addExpense: (expense: ExpenseCategory) => void;
  removeExpense: (id: string) => void;
  setRetirementDuration: (duration: number) => void;
  setCostResults: (results: CostResults) => void;
  setShowAddForm: (show: boolean) => void;
  setNewExpense: (expense: Partial<ExpenseCategory>) => void;
  resetNewExpense: () => void;

  // Retirement data actions
  setRetirementData: (data: RetirementData) => void;
  updateRetirementField: (field: keyof RetirementData, value: number) => void;

  // Global actions
  resetToDefaults: () => void;
}

const defaultNewExpense: Partial<ExpenseCategory> = {
  name: "",
  iconName: "Calculator",
  monthlyAmount: 0,
  frequency: "monthly",
  yearsInterval: 1,
};

const defaultRetirementData: RetirementData = {
  currentAge: 30,
  retirementAge: 60,
  currentPortfolio: 500000,
  monthlyContribution: 25000,
  annualReturn: 12,
  inflationRate: 6,
  contributionGrowthRate: 8,
  emergencyFund: 200000,
};

export const useAppStore = create<AppStore>()(
  persist(
    (set, get) => ({
      // Cost calculation state
      expenses: defaultExpenses,
      retirementDuration: 25,
      costResults: null,
      showAddForm: false,
      newExpense: defaultNewExpense,

      // Retirement data state
      retirementData: defaultRetirementData,

      // Cost calculation actions
      setExpenses: (expenses) => set({ expenses }),

      updateExpense: (id, field, value) =>
        set((state) => ({
          expenses: state.expenses.map((expense) =>
            expense.id === id ? { ...expense, [field]: value } : expense
          ),
        })),

      addExpense: (expense) =>
        set((state) => ({
          expenses: [...state.expenses, expense],
        })),

      removeExpense: (id) =>
        set((state) => ({
          expenses: state.expenses.filter((expense) => expense.id !== id),
        })),

      setRetirementDuration: (duration) =>
        set({ retirementDuration: duration }),

      setCostResults: (results) => {
        // Validate and migrate results to ensure compatibility
        const validatedResults = isValidCostResults(results)
          ? migrateCostResults(results)
          : null;
        set({ costResults: validatedResults });
      },

      setShowAddForm: (show) => set({ showAddForm: show }),

      setNewExpense: (expense) => set({ newExpense: expense }),

      resetNewExpense: () => set({ newExpense: defaultNewExpense }),

      // Retirement data actions
      setRetirementData: (data) => set({ retirementData: data }),

      updateRetirementField: (field, value) =>
        set((state) => ({
          retirementData: { ...state.retirementData, [field]: value },
        })),

      // Global actions
      resetToDefaults: () =>
        set({
          expenses: defaultExpenses,
          retirementDuration: 25,
          costResults: null,
          showAddForm: false,
          newExpense: defaultNewExpense,
          retirementData: defaultRetirementData,
        }),
    }),
    {
      name: "app-storage",
      partialize: (state) => ({
        expenses: state.expenses,
        retirementDuration: state.retirementDuration,
        costResults: state.costResults,
        retirementData: state.retirementData,
      }),
      // Migrate old data when loading from storage
      onRehydrateStorage: () => (state) => {
        if (state?.costResults) {
          // Migrate existing results to new structure
          state.costResults = migrateCostResults(state.costResults);
        }
      },
    }
  )
);
