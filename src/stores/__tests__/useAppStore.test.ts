import { describe, it, expect, beforeEach } from 'vitest'
import { useAppStore } from '../useAppStore'
import { ExpenseCategory } from '@/types/costCalculation'

// Helper to get a fresh store instance
const createStore = () => {
  const store = useAppStore.getState()
  store.resetToDefaults()
  return store
}

describe('useAppStore', () => {
  let store: ReturnType<typeof useAppStore.getState>

  beforeEach(() => {
    store = createStore()
  })

  describe('initial state', () => {
    it('should have default expenses', () => {
      expect(store.expenses).toBeDefined()
      expect(Array.isArray(store.expenses)).toBe(true)
      expect(store.expenses.length).toBeGreaterThan(0)
    })

    it('should have default retirement duration', () => {
      expect(store.retirementDuration).toBe(25)
    })

    it('should have default retirement data', () => {
      expect(store.retirementData).toBeDefined()
      expect(store.retirementData.currentAge).toBe(30)
      expect(store.retirementData.retirementAge).toBe(60)
      expect(store.retirementData.annualReturn).toBe(12)
    })

    it('should have initial form state', () => {
      expect(store.showAddForm).toBe(false)
      expect(store.costResults).toBeNull()
    })
  })

  describe('expense management', () => {
    it('should add new expense', () => {
      const initialCount = store.expenses.length
      const newExpense: ExpenseCategory = {
        id: 'test-1',
        name: 'Test Expense',
        iconName: 'TestIcon',
        monthlyAmount: 5000,
        frequency: 'monthly',
      }

      store.addExpense(newExpense)
      
      expect(store.expenses.length).toBe(initialCount + 1)
      expect(store.expenses.find(e => e.id === 'test-1')).toEqual(newExpense)
    })

    it('should remove expense', () => {
      const initialCount = store.expenses.length
      const firstExpenseId = store.expenses[0].id

      store.removeExpense(firstExpenseId)
      
      expect(store.expenses.length).toBe(initialCount - 1)
      expect(store.expenses.find(e => e.id === firstExpenseId)).toBeUndefined()
    })

    it('should update expense', () => {
      const firstExpense = store.expenses[0]
      const newAmount = 99999

      store.updateExpense(firstExpense.id, 'monthlyAmount', newAmount)
      
      const updatedExpense = store.expenses.find(e => e.id === firstExpense.id)
      expect(updatedExpense?.monthlyAmount).toBe(newAmount)
    })

    it('should set all expenses', () => {
      const newExpenses: ExpenseCategory[] = [
        {
          id: 'new-1',
          name: 'New Expense 1',
          iconName: 'Icon1',
          monthlyAmount: 1000,
          frequency: 'monthly',
        },
        {
          id: 'new-2',
          name: 'New Expense 2',
          iconName: 'Icon2',
          monthlyAmount: 2000,
          frequency: 'yearly',
        },
      ]

      store.setExpenses(newExpenses)
      
      expect(store.expenses).toEqual(newExpenses)
      expect(store.expenses.length).toBe(2)
    })
  })

  describe('retirement data management', () => {
    it('should update retirement data', () => {
      const newData = {
        currentAge: 35,
        retirementAge: 65,
        currentPortfolio: 2000000,
        monthlyContribution: 75000,
        annualReturn: 15,
        inflationRate: 4,
        contributionGrowthRate: 3,
        emergencyFund: 500000,
      }

      store.setRetirementData(newData)
      
      expect(store.retirementData).toEqual(newData)
    })

    it('should update individual retirement data fields', () => {
      store.updateRetirementData('currentAge', 40)
      store.updateRetirementData('annualReturn', 10)
      
      expect(store.retirementData.currentAge).toBe(40)
      expect(store.retirementData.annualReturn).toBe(10)
    })
  })

  describe('form state management', () => {
    it('should toggle add form visibility', () => {
      expect(store.showAddForm).toBe(false)
      
      store.setShowAddForm(true)
      expect(store.showAddForm).toBe(true)
      
      store.setShowAddForm(false)
      expect(store.showAddForm).toBe(false)
    })

    it('should manage new expense form data', () => {
      const partialExpense = {
        name: 'Test Expense',
        monthlyAmount: 5000,
      }

      store.setNewExpense(partialExpense)
      
      expect(store.newExpense.name).toBe('Test Expense')
      expect(store.newExpense.monthlyAmount).toBe(5000)
    })

    it('should reset new expense form', () => {
      store.setNewExpense({ name: 'Test', monthlyAmount: 1000 })
      store.resetNewExpense()
      
      expect(store.newExpense.name).toBe('')
      expect(store.newExpense.monthlyAmount).toBe(0)
      expect(store.newExpense.frequency).toBe('monthly')
    })
  })

  describe('cost results management', () => {
    it('should set cost results', () => {
      const mockResults = {
        currentMonthlyCost: 50000,
        currentAnnualCost: 600000,
        adjustedMonthlyCost: 75000,
        adjustedAnnualCost: 900000,
        totalRetirementCost: 22500000,
        oneTimeCosts: 2500000,
        breakdown: {
          monthly: 18750000,
          yearly: 1250000,
          oneTime: 2500000,
        },
        detailedBreakdown: {
          currentYearlyCost: 100000,
          adjustedYearlyCost: 150000,
          yearlyExpensesByCategory: [],
          oneTimeExpensesByCategory: [],
        },
      }

      store.setCostResults(mockResults)
      
      expect(store.costResults).toEqual(mockResults)
    })

    it('should set retirement duration', () => {
      store.setRetirementDuration(30)
      expect(store.retirementDuration).toBe(30)
    })
  })

  describe('reset functionality', () => {
    it('should reset to defaults', () => {
      // Modify state
      store.setRetirementDuration(30)
      store.setShowAddForm(true)
      store.updateRetirementData('currentAge', 40)
      store.setNewExpense({ name: 'Test' })

      // Reset
      store.resetToDefaults()

      // Check defaults are restored
      expect(store.retirementDuration).toBe(25)
      expect(store.showAddForm).toBe(false)
      expect(store.retirementData.currentAge).toBe(30)
      expect(store.newExpense.name).toBe('')
      expect(store.costResults).toBeNull()
    })
  })

  describe('edge cases', () => {
    it('should handle updating non-existent expense', () => {
      const initialExpenses = [...store.expenses]
      
      store.updateExpense('non-existent-id', 'monthlyAmount', 1000)
      
      // Should not modify any expenses
      expect(store.expenses).toEqual(initialExpenses)
    })

    it('should handle removing non-existent expense', () => {
      const initialCount = store.expenses.length
      
      store.removeExpense('non-existent-id')
      
      expect(store.expenses.length).toBe(initialCount)
    })

    it('should handle setting empty expenses array', () => {
      store.setExpenses([])
      expect(store.expenses).toEqual([])
      expect(store.expenses.length).toBe(0)
    })

    it('should handle invalid retirement data updates', () => {
      const initialData = { ...store.retirementData }
      
      // These should still work as TypeScript allows any value for the generic
      store.updateRetirementData('currentAge' as any, 'invalid' as any)
      
      // The store should handle this gracefully
      expect(store.retirementData.currentAge).toBe('invalid' as any)
    })
  })

  describe('persistence', () => {
    it('should have persistence configuration', () => {
      // This tests that the store is configured with persistence
      // The actual persistence behavior is handled by Zustand
      expect(useAppStore.persist).toBeDefined()
    })
  })

  describe('computed values and derived state', () => {
    it('should maintain consistency between related fields', () => {
      // Test that updating related fields maintains logical consistency
      store.updateRetirementData('currentAge', 40)
      store.updateRetirementData('retirementAge', 35) // Invalid: retirement before current age
      
      // The store should allow this (business logic validation should be elsewhere)
      expect(store.retirementData.currentAge).toBe(40)
      expect(store.retirementData.retirementAge).toBe(35)
    })

    it('should handle expense frequency changes correctly', () => {
      const expense = store.expenses[0]
      
      store.updateExpense(expense.id, 'frequency', 'one-time')
      
      const updatedExpense = store.expenses.find(e => e.id === expense.id)
      expect(updatedExpense?.frequency).toBe('one-time')
    })
  })
})
