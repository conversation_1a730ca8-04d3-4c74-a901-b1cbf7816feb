
export interface RetirementProjection {
  year: number;
  age: number;
  portfolioValue: number;
  realValue: number;
  annualContribution: number;
  cumulativeContributions: number;
}

export interface SustainabilityAnalysis {
  yearsUntilDepletion: number | null; // null means funds never run out
  ageWhenDepleted: number | null;
  issustainable: boolean;
  monthlyWithdrawalAmount: number;
  realMonthlyWithdrawal: number;
}

export interface InvestmentRecommendation {
  recommendedMonthlyInvestment: number;
  currentShortfall: number;
  additionalYearsNeeded: number;
  targetCorpus: number;
}

export const calculateRetirementProjection = (data: any) => {
  const yearsToRetirement = data.retirementAge - data.currentAge;
  const monthlyReturn = data.annualReturn / 100 / 12;
  const monthlyInflation = data.inflationRate / 100 / 12;
  const annualContributionGrowth = data.contributionGrowthRate / 100;
  
  let currentValue = data.currentPortfolio;
  let monthlyContribution = data.monthlyContribution;
  let totalContributions = 0;
  
  const yearlyProjections: RetirementProjection[] = [];
  
  // Calculate year by year
  for (let year = 1; year <= yearsToRetirement; year++) {
    const currentAge = data.currentAge + year;
    let yearStartValue = currentValue;
    let yearContributions = 0;
    
    // Calculate monthly for this year
    for (let month = 1; month <= 12; month++) {
      // Add monthly contribution
      currentValue += monthlyContribution;
      yearContributions += monthlyContribution;
      totalContributions += monthlyContribution;
      
      // Apply monthly return
      currentValue *= (1 + monthlyReturn);
    }
    
    // Increase monthly contribution for next year
    monthlyContribution *= (1 + annualContributionGrowth);
    
    // Calculate real value (adjusted for inflation)
    const inflationMultiplier = Math.pow(1 + data.inflationRate / 100, year);
    const realValue = currentValue / inflationMultiplier;
    
    yearlyProjections.push({
      year,
      age: currentAge,
      portfolioValue: currentValue,
      realValue,
      annualContribution: yearContributions,
      cumulativeContributions: totalContributions
    });
  }
  
  const finalValue = currentValue;
  const finalInflationMultiplier = Math.pow(1 + data.inflationRate / 100, yearsToRetirement);
  const realValue = finalValue / finalInflationMultiplier;
  
  return {
    yearsToRetirement,
    finalValue,
    realValue,
    totalContributions,
    yearlyProjections,
    monthlyRetirementIncome: (finalValue * 0.04) / 12, // 4% rule
    realMonthlyIncome: (realValue * 0.04) / 12
  };
};

export const calculateSustainability = (
  portfolioValue: number,
  monthlyExpenses: number,
  retirementAge: number,
  lifeExpectancy: number,
  inflationRate: number,
  postRetirementReturn: number
): SustainabilityAnalysis => {
  const yearsInRetirement = lifeExpectancy - retirementAge;
  const monthlyInflation = inflationRate / 100 / 12;
  const monthlyReturn = postRetirementReturn / 100 / 12;
  
  let currentPortfolio = portfolioValue;
  let currentAge = retirementAge;
  let monthsInRetirement = 0;
  
  // Simulate withdrawal for each month until portfolio depletes or life expectancy reached
  while (currentAge < lifeExpectancy && currentPortfolio > 0) {
    // Apply investment return
    currentPortfolio *= (1 + monthlyReturn);
    
    // Calculate inflation-adjusted monthly expenses
    const inflationAdjustedExpenses = monthlyExpenses * Math.pow(1 + monthlyInflation, monthsInRetirement);
    
    // Withdraw monthly expenses
    currentPortfolio -= inflationAdjustedExpenses;
    
    monthsInRetirement++;
    currentAge = retirementAge + (monthsInRetirement / 12);
  }
  
  const yearsUntilDepletion = currentPortfolio <= 0 ? monthsInRetirement / 12 : null;
  const ageWhenDepleted = yearsUntilDepletion ? retirementAge + yearsUntilDepletion : null;
  const issustainable = currentPortfolio > 0;
  
  return {
    yearsUntilDepletion,
    ageWhenDepleted,
    issustainable,
    monthlyWithdrawalAmount: monthlyExpenses,
    realMonthlyWithdrawal: monthlyExpenses
  };
};

export const calculateInvestmentRecommendation = (
  currentAge: number,
  retirementAge: number,
  currentPortfolio: number,
  monthlyExpenses: number,
  lifeExpectancy: number,
  inflationRate: number,
  annualReturn: number,
  postRetirementReturn: number
): InvestmentRecommendation => {
  const yearsToRetirement = retirementAge - currentAge;
  const yearsInRetirement = lifeExpectancy - retirementAge;
  
  // Calculate required corpus using PMT formula for sustainable withdrawal
  const monthlyInflation = inflationRate / 100 / 12;
  const monthlyPostReturn = postRetirementReturn / 100 / 12;
  const totalMonthsInRetirement = yearsInRetirement * 12;
  
  // Present value of all future expenses (inflation-adjusted)
  let requiredCorpus = 0;
  for (let month = 1; month <= totalMonthsInRetirement; month++) {
    const inflationAdjustedExpense = monthlyExpenses * Math.pow(1 + monthlyInflation, month - 1);
    const presentValue = inflationAdjustedExpense / Math.pow(1 + monthlyPostReturn, month - 1);
    requiredCorpus += presentValue;
  }
  
  // Calculate what current portfolio will grow to
  const futureValueOfCurrent = currentPortfolio * Math.pow(1 + annualReturn / 100, yearsToRetirement);
  
  // Calculate shortfall
  const shortfall = Math.max(0, requiredCorpus - futureValueOfCurrent);
  
  // Calculate required monthly investment using PMT formula
  const monthlyReturn = annualReturn / 100 / 12;
  const totalMonths = yearsToRetirement * 12;
  
  let recommendedMonthlyInvestment = 0;
  if (shortfall > 0 && totalMonths > 0) {
    // PMT formula: shortfall = PMT * [((1+r)^n - 1) / r]
    const factor = (Math.pow(1 + monthlyReturn, totalMonths) - 1) / monthlyReturn;
    recommendedMonthlyInvestment = shortfall / factor;
  }
  
  return {
    recommendedMonthlyInvestment,
    currentShortfall: shortfall,
    additionalYearsNeeded: 0, // Could be calculated if user wants to maintain current SIP
    targetCorpus: requiredCorpus
  };
};

export const calculateScenario = (baseData: any, modifications: any) => {
  const scenarioData = { ...baseData, ...modifications };
  return calculateRetirementProjection(scenarioData);
};

export const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('en-IN', {
    style: 'currency',
    currency: 'INR',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(amount);
};
