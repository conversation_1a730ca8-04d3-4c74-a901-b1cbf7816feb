// Test utility to verify cost calculation logic
import { ExpenseCategory } from '@/types/costCalculation';

export const testCostCalculation = () => {
  console.log('=== Testing Cost Calculation Logic ===');
  
  // Test data
  const testExpenses: ExpenseCategory[] = [
    { id: '1', name: 'Food', iconName: 'Utensils', monthlyAmount: 10000, frequency: 'monthly' },
    { id: '2', name: 'Vacation', iconName: 'Plane', monthlyAmount: 50000, frequency: 'yearly' },
    { id: '3', name: 'Laptop', iconName: 'Laptop', monthlyAmount: 80000, frequency: 'one-time', yearsInterval: 4 },
  ];
  
  const retirementDuration = 20; // 20 years
  const inflationRate = 6; // 6% per year
  const yearsToRetirement = 10; // 10 years to retirement
  
  console.log('Test Expenses:', testExpenses);
  console.log('Retirement Duration:', retirementDuration, 'years');
  console.log('Inflation Rate:', inflationRate, '%');
  console.log('Years to Retirement:', yearsToRetirement);
  
  // Calculate current costs
  let currentMonthlyCost = 0;
  let currentYearlyCost = 0;
  let currentOneTimeCosts = 0;
  
  testExpenses.forEach(expense => {
    if (expense.frequency === 'monthly') {
      currentMonthlyCost += expense.monthlyAmount;
    } else if (expense.frequency === 'yearly') {
      currentYearlyCost += expense.monthlyAmount;
    } else if (expense.frequency === 'one-time' && expense.yearsInterval) {
      let occurrences = 0;
      for (let year = 0; year < retirementDuration; year += expense.yearsInterval) {
        occurrences++;
      }
      currentOneTimeCosts += expense.monthlyAmount * occurrences;
      console.log(`One-time expense "${expense.name}": ${occurrences} occurrences over ${retirementDuration} years`);
    }
  });
  
  console.log('\n=== Current Costs ===');
  console.log('Monthly:', currentMonthlyCost);
  console.log('Yearly:', currentYearlyCost);
  console.log('One-time Total:', currentOneTimeCosts);
  
  // Calculate inflation-adjusted costs at retirement
  const inflationMultiplierAtRetirement = Math.pow(1 + inflationRate / 100, yearsToRetirement);
  const adjustedMonthlyCostAtRetirement = currentMonthlyCost * inflationMultiplierAtRetirement;
  const adjustedYearlyCostAtRetirement = currentYearlyCost * inflationMultiplierAtRetirement;
  
  console.log('\n=== Inflation-Adjusted Costs at Retirement ===');
  console.log('Inflation Multiplier:', inflationMultiplierAtRetirement.toFixed(2));
  console.log('Adjusted Monthly:', adjustedMonthlyCostAtRetirement.toFixed(0));
  console.log('Adjusted Yearly:', adjustedYearlyCostAtRetirement.toFixed(0));
  
  // Calculate total retirement costs with progressive inflation
  let totalMonthlyExpenses = 0;
  let totalYearlyExpenses = 0;
  let totalOneTimeExpenses = 0;
  
  // Calculate one-time expenses with inflation applied at occurrence time
  testExpenses.forEach(expense => {
    if (expense.frequency === 'one-time' && expense.yearsInterval) {
      for (let year = 0; year < retirementDuration; year += expense.yearsInterval) {
        const yearInflationMultiplier = Math.pow(1 + inflationRate / 100, yearsToRetirement + year);
        const adjustedExpenseAtOccurrence = expense.monthlyAmount * yearInflationMultiplier;
        totalOneTimeExpenses += adjustedExpenseAtOccurrence;
        console.log(`${expense.name} at year ${year}: ₹${adjustedExpenseAtOccurrence.toFixed(0)}`);
      }
    }
  });
  
  // Calculate monthly and yearly expenses for each year
  for (let year = 0; year < retirementDuration; year++) {
    const yearInflationMultiplier = Math.pow(1 + inflationRate / 100, year);
    const yearlyMonthlyCost = adjustedMonthlyCostAtRetirement * yearInflationMultiplier * 12;
    const yearlyYearlyCost = adjustedYearlyCostAtRetirement * yearInflationMultiplier;
    
    totalMonthlyExpenses += yearlyMonthlyCost;
    totalYearlyExpenses += yearlyYearlyCost;
  }
  
  const totalRetirementCost = totalMonthlyExpenses + totalYearlyExpenses + totalOneTimeExpenses;
  
  console.log('\n=== Total Retirement Costs ===');
  console.log('Total Monthly Expenses:', totalMonthlyExpenses.toFixed(0));
  console.log('Total Yearly Expenses:', totalYearlyExpenses.toFixed(0));
  console.log('Total One-time Expenses:', totalOneTimeExpenses.toFixed(0));
  console.log('TOTAL RETIREMENT COST:', totalRetirementCost.toFixed(0));
  
  console.log('\n=== Test Complete ===');
  
  return {
    currentMonthlyCost,
    currentYearlyCost,
    currentOneTimeCosts,
    totalRetirementCost,
    totalMonthlyExpenses,
    totalYearlyExpenses,
    totalOneTimeExpenses
  };
};
