import { CostResults } from "@/types/costCalculation";

/**
 * Default values for detailed breakdown to prevent undefined access errors
 */
const DEFAULT_DETAILED_BREAKDOWN = {
  currentYearlyCost: 0,
  adjustedYearlyCost: 0,
  yearlyExpensesByCategory: [],
  oneTimeExpensesByCategory: [],
};

/**
 * Safely gets the detailed breakdown from cost results with fallback defaults
 * This prevents TypeError when accessing properties that might not exist in older data
 */
export const getSafeDetailedBreakdown = (
  costResults: CostResults | null | undefined
) => {
  if (!costResults) {
    return DEFAULT_DETAILED_BREAKDOWN;
  }

  // Check if detailedBreakdown exists and has the expected structure
  if (
    !costResults.detailedBreakdown ||
    typeof costResults.detailedBreakdown !== "object"
  ) {
    return DEFAULT_DETAILED_BREAKDOWN;
  }

  // Ensure all required properties exist with safe defaults
  return {
    currentYearlyCost: costResults.detailedBreakdown.currentYearlyCost ?? 0,
    adjustedYearlyCost: costResults.detailedBreakdown.adjustedYearlyCost ?? 0,
    yearlyExpensesByCategory: Array.isArray(
      costResults.detailedBreakdown.yearlyExpensesByCategory
    )
      ? costResults.detailedBreakdown.yearlyExpensesByCategory
      : [],
    oneTimeExpensesByCategory: Array.isArray(
      costResults.detailedBreakdown.oneTimeExpensesByCategory
    )
      ? costResults.detailedBreakdown.oneTimeExpensesByCategory
      : [],
  };
};

/**
 * Safely gets a numeric value from cost results with fallback
 */
export const getSafeCostValue = (
  costResults: CostResults | null | undefined,
  property: keyof CostResults,
  defaultValue: number = 0
): number => {
  if (!costResults || typeof costResults[property] !== "number") {
    return defaultValue;
  }
  return costResults[property] as number;
};

/**
 * Checks if cost results have the new detailed breakdown structure
 */
export const hasDetailedBreakdown = (
  costResults: CostResults | null | undefined
): boolean => {
  return !!(
    costResults?.detailedBreakdown &&
    typeof costResults.detailedBreakdown === "object" &&
    Array.isArray(costResults.detailedBreakdown.yearlyExpensesByCategory) &&
    Array.isArray(costResults.detailedBreakdown.oneTimeExpensesByCategory)
  );
};

/**
 * Validates and migrates old cost results to new structure if needed
 */
export const migrateCostResults = (
  costResults: CostResults | null
): CostResults | null => {
  if (!costResults) return null;

  // If already has detailed breakdown, return as is
  if (hasDetailedBreakdown(costResults)) {
    return costResults;
  }

  // Migrate old structure to new structure
  const migratedResults: CostResults = {
    ...costResults,
    detailedBreakdown: DEFAULT_DETAILED_BREAKDOWN,
  };

  return migratedResults;
};

/**
 * Type guard to check if cost results are valid and complete
 */
export const isValidCostResults = (
  costResults: any
): costResults is CostResults => {
  return (
    costResults &&
    typeof costResults === "object" &&
    typeof costResults.currentMonthlyCost === "number" &&
    typeof costResults.currentAnnualCost === "number" &&
    typeof costResults.adjustedMonthlyCost === "number" &&
    typeof costResults.adjustedAnnualCost === "number" &&
    typeof costResults.totalRetirementCost === "number" &&
    typeof costResults.oneTimeCosts === "number" &&
    costResults.breakdown &&
    typeof costResults.breakdown === "object"
  );
};

/**
 * Comprehensive error boundary for cost data access
 * Wraps any cost data access in a try-catch with fallback values
 */
export const safeAccessCostData = <T>(
  accessor: () => T,
  fallback: T,
  errorMessage?: string
): T => {
  try {
    const result = accessor();
    return result !== undefined && result !== null ? result : fallback;
  } catch (error) {
    if (errorMessage) {
      console.warn(`Safe access failed: ${errorMessage}`, error);
    }
    return fallback;
  }
};

/**
 * Creates a safe accessor function for nested object properties
 */
export const createSafeAccessor = <T extends object, K extends keyof T>(
  obj: T | null | undefined,
  defaultValue: T[K]
) => {
  return (key: K): T[K] => {
    return safeAccessCostData(
      () => obj?.[key] as T[K],
      defaultValue,
      `Accessing property ${String(key)} on cost data`
    );
  };
};
