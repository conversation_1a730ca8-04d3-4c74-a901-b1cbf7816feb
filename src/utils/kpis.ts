export function getReturnMultiple(
  finalValue: number,
  totalContributions: number
): number {
  if (totalContributions === 0) return 0;
  return finalValue / totalContributions;
}

export function getTotalReturn(
  finalValue: number,
  totalContributions: number
): number {
  return finalValue - totalContributions;
}

export function getAnnualCoveragePct(
  monthlyIncome: number,
  adjustedAnnualCost: number
): number {
  if (adjustedAnnualCost === 0) return 0;
  return ((monthlyIncome * 12) / adjustedAnnualCost) * 100;
}

export function getNetMonthly(
  monthlyIncome: number,
  adjustedMonthlyCost: number
): number {
  return monthlyIncome - adjustedMonthlyCost;
}

export function getFundDuration(
  fundDepletionAge: number | undefined,
  retirementAge: number,
  retirementDuration: number
): string {
  if (fundDepletionAge) {
    return `${fundDepletionAge - retirementAge} years`;
  }
  return `${Math.max(0, retirementDuration || 0)}+ years`;
}
