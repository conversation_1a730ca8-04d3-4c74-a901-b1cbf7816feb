import { describe, it, expect } from "vitest";
import {
  toChartData,
  getFundDepletionAge,
  getOneTimeCostDetailsForAge,
  getYearlyExpensesForAge,
  AXIS_LABELS,
  SERIES_KEYS,
  ChartDatum,
} from "../chart";
import { ProjectionPoint, OneTimeOccurrence } from "../calculationService";
import { ExpenseCategory } from "@/types/costCalculation";

describe("chart utilities", () => {
  const mockProjections: ProjectionPoint[] = [
    {
      age: 30,
      year: 2024,
      portfolioValue: 1000000,
      realValue: 1000000,
      cumulativeContributions: 500000,
    },
    {
      age: 35,
      year: 2029,
      portfolioValue: 2000000,
      realValue: 1800000,
      cumulativeContributions: 800000,
    },
    {
      age: 65,
      year: 2059,
      portfolioValue: 10000000,
      realValue: 8000000,
      cumulativeContributions: 2000000,
      isRetirement: true,
      annualCost: 600000,
      consolidatedCosts: 650000,
      oneTimeCosts: 50000,
    },
    {
      age: 70,
      year: 2064,
      portfolioValue: 8000000,
      realValue: 6000000,
      cumulativeContributions: 2000000,
      isRetirement: true,
      annualCost: 600000,
      consolidatedCosts: 600000,
    },
    {
      age: 85,
      year: 2079,
      portfolioValue: 0,
      realValue: 0,
      cumulativeContributions: 2000000,
      isRetirement: true,
      annualCost: 600000,
      consolidatedCosts: 600000,
    },
  ];

  const mockOneTimeOccurrences: OneTimeOccurrence[] = [
    {
      age: 65,
      year: 2059,
      expenseName: "Car Replacement",
      amount: 50000,
      totalAmount: 50000,
    },
    {
      age: 70,
      year: 2064,
      expenseName: "Home Renovation",
      amount: 200000,
      totalAmount: 200000,
    },
  ];

  describe("toChartData", () => {
    it("should convert projections to chart data format", () => {
      const chartData = toChartData(mockProjections);

      expect(chartData).toHaveLength(5);
      expect(chartData[0]).toHaveProperty("age", 30);
      expect(chartData[0]).toHaveProperty("Portfolio Value", 1000000);
      expect(chartData[0]).toHaveProperty("Real Value", 1000000);
    });

    it("should handle empty projections array", () => {
      const chartData = toChartData([]);
      expect(chartData).toEqual([]);
    });

    it("should preserve all projection properties", () => {
      const chartData = toChartData(mockProjections);
      const retirementPoint = chartData.find((point) => point.isRetirement);

      expect(retirementPoint).toHaveProperty("annualCost", 600000);
      expect(retirementPoint).toHaveProperty("consolidatedCosts");
      expect(retirementPoint).toHaveProperty("oneTimeCosts");
    });

    it("should handle projections without optional properties", () => {
      const minimalProjections: ProjectionPoint[] = [
        {
          age: 30,
          year: 2024,
          portfolioValue: 1000000,
          realValue: 1000000,
          cumulativeContributions: 500000,
        },
      ];

      const chartData = toChartData(minimalProjections);
      expect(chartData).toHaveLength(1);
      expect(chartData[0].annualCost).toBeUndefined();
    });
  });

  describe("getFundDepletionAge", () => {
    it("should find the age when funds are depleted", () => {
      const chartData = toChartData(mockProjections);
      const depletionAge = getFundDepletionAge(chartData);
      expect(depletionAge).toBe(85);
    });

    it("should return undefined if funds never deplete", () => {
      const sustainableProjections = mockProjections.filter(
        (p) => p.portfolioValue > 0
      );
      const chartData = toChartData(sustainableProjections);
      const depletionAge = getFundDepletionAge(chartData);
      expect(depletionAge).toBeUndefined();
    });

    it("should handle empty projections", () => {
      const depletionAge = getFundDepletionAge([]);
      expect(depletionAge).toBeUndefined();
    });

    it("should handle projections starting with zero portfolio", () => {
      const zeroProjections: ProjectionPoint[] = [
        {
          age: 30,
          year: 2024,
          portfolioValue: 0,
          realValue: 0,
          cumulativeContributions: 0,
        },
      ];

      const depletionAge = getFundDepletionAge(zeroProjections);
      expect(depletionAge).toBe(30);
    });
  });

  describe("getOneTimeCostDetailsForAge", () => {
    it("should return one-time costs for specific age", () => {
      const details = getOneTimeCostDetailsForAge(mockOneTimeOccurrences, 65);

      expect(details).toHaveLength(1);
      expect(details[0].expenseName).toBe("Car Replacement");
      expect(details[0].amount).toBe(50000);
    });

    it("should return empty array for age with no one-time costs", () => {
      const details = getOneTimeCostDetailsForAge(mockOneTimeOccurrences, 75);
      expect(details).toEqual([]);
    });

    it("should handle multiple one-time costs for same age", () => {
      const multipleOccurrences: OneTimeOccurrence[] = [
        ...mockOneTimeOccurrences,
        {
          age: 65,
          year: 2059,
          expenseName: "Medical Equipment",
          amount: 30000,
          totalAmount: 30000,
        },
      ];

      const details = getOneTimeCostDetailsForAge(multipleOccurrences, 65);
      expect(details).toHaveLength(2);
    });

    it("should handle empty occurrences array", () => {
      const details = getOneTimeCostDetailsForAge([], 65);
      expect(details).toEqual([]);
    });
  });

  describe("getYearlyExpensesForAge", () => {
    const mockExpenses: ExpenseCategory[] = [
      {
        id: "1",
        name: "Monthly Rent",
        iconName: "Home",
        monthlyAmount: 15000,
        frequency: "monthly",
      },
      {
        id: "2",
        name: "Annual Insurance",
        iconName: "Shield",
        monthlyAmount: 50000,
        frequency: "yearly",
      },
    ];

    it("should return yearly expenses for specific age", () => {
      const expenses = getYearlyExpensesForAge(mockExpenses, 67, 65, 35, 3);

      expect(Array.isArray(expenses)).toBe(true);
      expect(expenses.length).toBeGreaterThan(0);
    });

    it("should return empty array for age before retirement", () => {
      const expenses = getYearlyExpensesForAge(mockExpenses, 60, 65, 35, 3);
      expect(expenses).toEqual([]);
    });

    it("should return empty array for age not in retirement", () => {
      const expenses = getYearlyExpensesForAge(mockExpenses, 30, 65, 35, 3);
      expect(expenses).toEqual([]);
    });

    it("should handle projections without expense data", () => {
      const projectionsWithoutExpenses: ProjectionPoint[] = [
        {
          age: 65,
          year: 2059,
          portfolioValue: 10000000,
          realValue: 8000000,
          cumulativeContributions: 2000000,
          isRetirement: true,
        },
      ];

      const expenses = getYearlyExpensesForAge(65, projectionsWithoutExpenses);
      expect(expenses).toEqual({
        annualCost: undefined,
        consolidatedCosts: undefined,
        oneTimeCosts: undefined,
      });
    });
  });

  describe("constants", () => {
    it("should have proper axis labels", () => {
      expect(AXIS_LABELS).toHaveProperty("xAge");
      expect(AXIS_LABELS).toHaveProperty("yValueCurrency");

      expect(typeof AXIS_LABELS.xAge).toBe("string");
      expect(typeof AXIS_LABELS.yValueCurrency).toBe("string");
    });

    it("should have proper series keys", () => {
      expect(SERIES_KEYS).toHaveProperty("portfolioValue");
      expect(SERIES_KEYS).toHaveProperty("realValue");
      expect(SERIES_KEYS).toHaveProperty("cumulativeContributions");

      expect(typeof SERIES_KEYS.portfolioValue).toBe("string");
      expect(typeof SERIES_KEYS.realValue).toBe("string");
    });

    it("should have consistent series key values", () => {
      expect(SERIES_KEYS.portfolioValue).toBe("Portfolio Value");
      expect(SERIES_KEYS.realValue).toBe("Real Value");
      expect(SERIES_KEYS.cumulativeContributions).toBe(
        "Cumulative Contributions"
      );
    });
  });

  describe("edge cases and error handling", () => {
    it("should handle projections with negative portfolio values", () => {
      const negativeProjections: ProjectionPoint[] = [
        {
          age: 30,
          year: 2024,
          portfolioValue: -100000,
          realValue: -100000,
          cumulativeContributions: 500000,
        },
      ];

      const chartData = toChartData(negativeProjections);
      expect(chartData[0]["Portfolio Value"]).toBe(-100000);

      const depletionAge = getFundDepletionAge(chartData);
      expect(depletionAge).toBe(30);
    });

    it("should handle very large numbers", () => {
      const largeProjections: ProjectionPoint[] = [
        {
          age: 30,
          year: 2024,
          portfolioValue: 1e12, // 1 trillion
          realValue: 1e12,
          cumulativeContributions: 1e11,
        },
      ];

      const chartData = toChartData(largeProjections);
      expect(chartData[0]["Portfolio Value"]).toBe(1e12);
    });

    it("should handle duplicate ages in projections", () => {
      const duplicateProjections: ProjectionPoint[] = [
        {
          age: 30,
          year: 2024,
          portfolioValue: 1000000,
          realValue: 1000000,
          cumulativeContributions: 500000,
        },
        {
          age: 30,
          year: 2024,
          portfolioValue: 1100000,
          realValue: 1100000,
          cumulativeContributions: 500000,
        },
      ];

      const chartData = toChartData(duplicateProjections);
      expect(chartData).toHaveLength(2);

      const expenses = getYearlyExpensesForAge(30, duplicateProjections);
      // Should return the first match
      expect(expenses).toBeDefined();
    });

    it("should handle unsorted projections", () => {
      const unsortedProjections = [...mockProjections].reverse();

      const chartData = toChartData(unsortedProjections);
      expect(chartData).toHaveLength(mockProjections.length);

      const depletionAge = getFundDepletionAge(chartData);
      expect(depletionAge).toBe(85); // Should still find the correct age
    });
  });
});
