import { describe, it, expect } from "vitest";
import {
  getAnnualCoveragePct,
  getFundDuration,
  getNetMonthly,
  getReturnMultiple,
  getTotalReturn,
} from "../kpis";

describe("KPI calculations", () => {
  describe("getAnnualCoveragePct", () => {
    it("should calculate annual coverage percentage correctly", () => {
      const coverage = getAnnualCoveragePct(50000, 600000); // 50k monthly income, 600k annual cost

      expect(coverage).toBe(100); // (50k * 12) / 600k * 100 = 100%
    });

    it("should return coverage over 100% when income exceeds expenses", () => {
      const coverage = getAnnualCoveragePct(60000, 600000); // 60k monthly income, 600k annual cost
      expect(coverage).toBe(120); // (60k * 12) / 600k * 100 = 120%
    });

    it("should return 0% when no income", () => {
      const coverage = getAnnualCoveragePct(0, 600000);
      expect(coverage).toBe(0);
    });

    it("should return 0% when no annual cost", () => {
      const coverage = getAnnualCoveragePct(50000, 0);
      expect(coverage).toBe(0);
    });

    it("should handle partial coverage", () => {
      const coverage = getAnnualCoveragePct(25000, 600000); // 25k monthly income, 600k annual cost
      expect(coverage).toBe(50); // (25k * 12) / 600k * 100 = 50%
    });
  });

  describe("getFundDuration", () => {
    it("should calculate fund duration correctly", () => {
      const duration = getFundDuration(85, 65, 25); // Depletes at 85, retirement at 65, 25 year duration

      expect(duration).toBe("20 years"); // 85 - 65 = 20 years
    });

    it("should return full duration plus when funds never deplete", () => {
      const duration = getFundDuration(undefined, 65, 25); // No depletion age

      expect(duration).toBe("25+ years");
    });

    it("should handle zero retirement duration", () => {
      const duration = getFundDuration(undefined, 65, 0);
      expect(duration).toBe("0+ years");
    });

    it("should handle immediate fund depletion", () => {
      const duration = getFundDuration(65, 65, 25); // Depletes immediately at retirement
      expect(duration).toBe("0 years");
    });

    it("should handle negative duration gracefully", () => {
      const duration = getFundDuration(60, 65, 25); // Depletes before retirement (edge case)
      expect(duration).toBe("-5 years");
    });
  });

  describe("getNetMonthly", () => {
    it("should calculate net monthly position correctly", () => {
      const netMonthly = getNetMonthly(50000, 30000); // 50k income, 30k expenses

      expect(netMonthly).toBe(20000); // 50k - 30k = 20k surplus
    });

    it("should return negative for deficit", () => {
      const netMonthly = getNetMonthly(30000, 50000); // 30k income, 50k expenses
      expect(netMonthly).toBe(-20000); // 30k - 50k = -20k deficit
    });

    it("should return zero for break-even", () => {
      const netMonthly = getNetMonthly(40000, 40000); // Equal income and expenses
      expect(netMonthly).toBe(0);
    });

    it("should handle zero income", () => {
      const netMonthly = getNetMonthly(0, 30000);
      expect(netMonthly).toBe(-30000);
    });

    it("should handle zero expenses", () => {
      const netMonthly = getNetMonthly(50000, 0);
      expect(netMonthly).toBe(50000);
    });
  });

  describe("getReturnMultiple", () => {
    it("should calculate return multiple correctly", () => {
      const returnMultiple = getReturnMultiple(10000000, 2000000); // 10M final, 2M contributions

      expect(returnMultiple).toBe(5); // 10M / 2M = 5x
    });

    it("should return 1 when no contributions", () => {
      const returnMultiple = getReturnMultiple(1000000, 0);
      expect(returnMultiple).toBe(1);
    });

    it("should handle zero final value", () => {
      const returnMultiple = getReturnMultiple(0, 2000000);
      expect(returnMultiple).toBe(0);
    });

    it("should handle equal final value and contributions", () => {
      const returnMultiple = getReturnMultiple(1000000, 1000000);
      expect(returnMultiple).toBe(1);
    });

    it("should handle loss scenarios", () => {
      const returnMultiple = getReturnMultiple(500000, 1000000); // Lost half
      expect(returnMultiple).toBe(0.5);
    });
  });

  describe("getTotalReturn", () => {
    it("should calculate total return correctly", () => {
      const totalReturn = getTotalReturn(10000000, 2000000); // 10M final, 2M contributions

      expect(totalReturn).toBe(8000000); // 10M - 2M = 8M return
    });

    it("should return 0 when final value equals contributions", () => {
      const totalReturn = getTotalReturn(2000000, 2000000);
      expect(totalReturn).toBe(0);
    });

    it("should return negative value for losses", () => {
      const totalReturn = getTotalReturn(1000000, 2000000); // Lost 1M
      expect(totalReturn).toBe(-1000000);
    });

    it("should handle zero contributions", () => {
      const totalReturn = getTotalReturn(1000000, 0);
      expect(totalReturn).toBe(1000000);
    });

    it("should handle zero final value", () => {
      const totalReturn = getTotalReturn(0, 2000000);
      expect(totalReturn).toBe(-2000000);
    });
  });

  describe("edge cases and error handling", () => {
    it("should handle very large numbers", () => {
      const returnMultiple = getReturnMultiple(1e12, 1e11); // 1 trillion / 100 billion
      expect(returnMultiple).toBe(10);

      const totalReturn = getTotalReturn(1e12, 1e11);
      expect(totalReturn).toBe(9e11);
    });

    it("should handle negative portfolio values", () => {
      const returnMultiple = getReturnMultiple(-1000000, 2000000);
      expect(returnMultiple).toBe(-0.5);

      const totalReturn = getTotalReturn(-1000000, 2000000);
      expect(totalReturn).toBe(-3000000);
    });

    it("should handle extreme coverage scenarios", () => {
      const highCoverage = getAnnualCoveragePct(100000, 600000); // Very high income
      expect(highCoverage).toBe(200); // 200% coverage

      const lowCoverage = getAnnualCoveragePct(1000, 600000); // Very low income
      expect(lowCoverage).toBe(2); // 2% coverage
    });

    it("should handle fund duration edge cases", () => {
      const veryLongDuration = getFundDuration(undefined, 30, 100);
      expect(veryLongDuration).toBe("100+ years");

      const negativeDuration = getFundDuration(60, 65, 25);
      expect(negativeDuration).toBe("-5 years");
    });
  });
});
