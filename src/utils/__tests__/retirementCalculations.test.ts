import { describe, it, expect } from 'vitest'
import {
  calculateProjections,
  calculateSustainability,
  calculateInvestmentRecommendation,
  formatCurrency,
} from '../retirementCalculations'

describe('retirementCalculations', () => {
  describe('calculateProjections', () => {
    it('should calculate correct projections for given parameters', () => {
      const projections = calculateProjections(
        35, // currentAge
        65, // retirementAge
        1000000, // currentPortfolio
        50000, // monthlyContribution
        12, // annualReturn
        3, // inflationRate
        2 // contributionGrowthRate
      )

      expect(projections.yearlyProjections).toHaveLength(30) // 65 - 35 = 30 years
      expect(projections.finalValue).toBeGreaterThan(1000000)
      expect(projections.totalContributions).toBeGreaterThan(0)
      expect(projections.monthlyRetirementIncome).toBeGreaterThan(0)
    })

    it('should show portfolio growth over time', () => {
      const projections = calculateProjections(35, 65, 1000000, 50000, 12, 3, 2)
      const firstYear = projections.yearlyProjections[0]
      const lastYear = projections.yearlyProjections[projections.yearlyProjections.length - 1]

      expect(lastYear.portfolioValue).toBeGreaterThan(firstYear.portfolioValue)
    })

    it('should account for contribution growth rate', () => {
      const projectionsWithGrowth = calculateProjections(35, 65, 1000000, 50000, 12, 3, 5)
      const projectionsWithoutGrowth = calculateProjections(35, 65, 1000000, 50000, 12, 3, 0)

      expect(projectionsWithGrowth.finalValue).toBeGreaterThan(projectionsWithoutGrowth.finalValue)
    })

    it('should handle zero monthly contribution', () => {
      const projections = calculateProjections(35, 65, 1000000, 0, 12, 3, 0)
      
      expect(projections.totalContributions).toBe(0)
      expect(projections.finalValue).toBeGreaterThan(1000000) // Should still grow due to returns
    })

    it('should handle edge case of retirement age equal to current age', () => {
      const projections = calculateProjections(65, 65, 1000000, 50000, 12, 3, 2)
      
      expect(projections.yearlyProjections).toHaveLength(0)
      expect(projections.finalValue).toBe(1000000)
    })
  })

  describe('calculateSustainability', () => {
    it('should determine if portfolio is sustainable', () => {
      const sustainability = calculateSustainability(
        65, // retirementAge
        10000000, // portfolioValue
        50000, // monthlyExpenses
        90, // lifeExpectancy
        3, // inflationRate
        8 // postRetirementReturn
      )

      expect(sustainability).toHaveProperty('yearsUntilDepletion')
      expect(sustainability).toHaveProperty('ageWhenDepleted')
      expect(sustainability).toHaveProperty('issustainable')
      expect(sustainability).toHaveProperty('monthlyWithdrawalAmount')
    })

    it('should identify sustainable portfolio', () => {
      const sustainability = calculateSustainability(65, 20000000, 50000, 90, 3, 8)
      
      expect(sustainability.issustainable).toBe(true)
      expect(sustainability.yearsUntilDepletion).toBeNull()
      expect(sustainability.ageWhenDepleted).toBeNull()
    })

    it('should identify unsustainable portfolio', () => {
      const sustainability = calculateSustainability(65, 1000000, 100000, 90, 3, 2)
      
      expect(sustainability.issustainable).toBe(false)
      expect(sustainability.yearsUntilDepletion).toBeGreaterThan(0)
      expect(sustainability.ageWhenDepleted).toBeGreaterThan(65)
    })

    it('should handle high inflation scenarios', () => {
      const highInflation = calculateSustainability(65, 10000000, 50000, 90, 10, 8)
      const lowInflation = calculateSustainability(65, 10000000, 50000, 90, 2, 8)
      
      // High inflation should make portfolio less sustainable
      if (highInflation.yearsUntilDepletion && lowInflation.yearsUntilDepletion) {
        expect(highInflation.yearsUntilDepletion).toBeLessThan(lowInflation.yearsUntilDepletion)
      }
    })
  })

  describe('calculateInvestmentRecommendation', () => {
    it('should provide investment recommendations', () => {
      const recommendation = calculateInvestmentRecommendation(
        35, // currentAge
        65, // retirementAge
        1000000, // currentPortfolio
        50000, // monthlyExpenses
        90, // lifeExpectancy
        3, // inflationRate
        12, // annualReturn
        8 // postRetirementReturn
      )

      expect(recommendation).toHaveProperty('requiredCorpus')
      expect(recommendation).toHaveProperty('currentProjectedValue')
      expect(recommendation).toHaveProperty('shortfall')
      expect(recommendation).toHaveProperty('recommendedMonthlyInvestment')
      expect(recommendation).toHaveProperty('isOnTrack')
    })

    it('should identify when on track for retirement', () => {
      const recommendation = calculateInvestmentRecommendation(
        35, 65, 20000000, 30000, 90, 3, 12, 8
      )
      
      expect(recommendation.isOnTrack).toBe(true)
      expect(recommendation.shortfall).toBe(0)
      expect(recommendation.recommendedMonthlyInvestment).toBe(0)
    })

    it('should calculate shortfall when not on track', () => {
      const recommendation = calculateInvestmentRecommendation(
        35, 65, 100000, 100000, 90, 3, 12, 8
      )
      
      expect(recommendation.isOnTrack).toBe(false)
      expect(recommendation.shortfall).toBeGreaterThan(0)
      expect(recommendation.recommendedMonthlyInvestment).toBeGreaterThan(0)
    })

    it('should handle edge case of no time to retirement', () => {
      const recommendation = calculateInvestmentRecommendation(
        65, 65, 1000000, 50000, 90, 3, 12, 8
      )
      
      expect(recommendation.recommendedMonthlyInvestment).toBe(0)
    })

    it('should provide higher recommendations for higher expenses', () => {
      const lowExpense = calculateInvestmentRecommendation(35, 65, 1000000, 30000, 90, 3, 12, 8)
      const highExpense = calculateInvestmentRecommendation(35, 65, 1000000, 80000, 90, 3, 12, 8)
      
      expect(highExpense.recommendedMonthlyInvestment).toBeGreaterThan(lowExpense.recommendedMonthlyInvestment)
    })
  })

  describe('formatCurrency', () => {
    it('should format currency correctly', () => {
      expect(formatCurrency(1000)).toBe('₹1,000')
      expect(formatCurrency(1000000)).toBe('₹10,00,000')
      expect(formatCurrency(10000000)).toBe('₹1,00,00,000')
    })

    it('should handle zero and negative values', () => {
      expect(formatCurrency(0)).toBe('₹0')
      expect(formatCurrency(-1000)).toBe('-₹1,000')
    })

    it('should handle decimal values', () => {
      expect(formatCurrency(1000.50)).toBe('₹1,001') // Should round
      expect(formatCurrency(999.49)).toBe('₹999')
    })

    it('should handle very large numbers', () => {
      expect(formatCurrency(1000000000)).toBe('₹100,00,00,000')
    })
  })

  describe('edge cases and error handling', () => {
    it('should handle negative portfolio values', () => {
      const projections = calculateProjections(35, 65, -1000000, 50000, 12, 3, 2)
      expect(projections.finalValue).toBeDefined()
    })

    it('should handle zero return rates', () => {
      const projections = calculateProjections(35, 65, 1000000, 50000, 0, 3, 2)
      expect(projections.finalValue).toBeDefined()
    })

    it('should handle negative return rates', () => {
      const projections = calculateProjections(35, 65, 1000000, 50000, -5, 3, 2)
      expect(projections.finalValue).toBeDefined()
    })

    it('should handle very high inflation rates', () => {
      const sustainability = calculateSustainability(65, 10000000, 50000, 90, 50, 8)
      expect(sustainability).toBeDefined()
    })

    it('should handle zero monthly expenses', () => {
      const sustainability = calculateSustainability(65, 10000000, 0, 90, 3, 8)
      expect(sustainability.issustainable).toBe(true)
    })
  })
})
