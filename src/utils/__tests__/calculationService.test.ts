import { describe, it, expect, beforeEach } from 'vitest'
import { CalculationService, calculateCosts, calculateYearlyBreakdowns } from '../calculationService'
import { ExpenseCategory } from '@/types/costCalculation'

describe('CalculationService', () => {
  let sampleExpenses: ExpenseCategory[]
  let service: CalculationService

  beforeEach(() => {
    sampleExpenses = [
      {
        id: '1',
        name: 'Monthly Rent',
        iconName: 'Home',
        monthlyAmount: 15000,
        frequency: 'monthly',
      },
      {
        id: '2',
        name: 'Annual Insurance',
        iconName: 'Shield',
        monthlyAmount: 50000,
        frequency: 'yearly',
      },
      {
        id: '3',
        name: 'Car Replacement',
        iconName: 'Car',
        monthlyAmount: 500000,
        frequency: 'one-time',
        yearsInterval: 5,
      },
    ]

    service = new CalculationService(
      sampleExpenses,
      65, // retirementAge
      25, // retirementDuration
      35, // currentAge
      3, // inflationRate
      12 // annualReturn
    )
  })

  describe('constructor', () => {
    it('should initialize with correct parameters', () => {
      expect(service).toBeDefined()
    })

    it('should use default inflation rate when not provided', () => {
      const defaultService = new CalculationService(sampleExpenses, 65, 25, 35)
      expect(defaultService).toBeDefined()
    })
  })

  describe('calculateAll', () => {
    it('should return all required calculation results', () => {
      const results = service.calculateAll()

      expect(results).toHaveProperty('yearlyBreakdowns')
      expect(results).toHaveProperty('aggregatedResults')
      expect(results).toHaveProperty('costResults')
      expect(results).toHaveProperty('oneTimeOccurrences')
    })

    it('should calculate correct number of yearly breakdowns', () => {
      const results = service.calculateAll()
      expect(results.yearlyBreakdowns).toHaveLength(25)
    })

    it('should calculate inflation-adjusted costs correctly', () => {
      const results = service.calculateAll()
      const firstYear = results.yearlyBreakdowns[0]
      const lastYear = results.yearlyBreakdowns[24]

      // Last year should have higher costs due to inflation
      expect(lastYear.totalYearlyExpenses).toBeGreaterThan(firstYear.totalYearlyExpenses)
    })

    it('should handle one-time expenses correctly', () => {
      const results = service.calculateAll()
      
      // Should have one-time occurrences every 5 years
      const oneTimeOccurrences = results.oneTimeOccurrences
      expect(oneTimeOccurrences.length).toBeGreaterThan(0)
      
      // Check that occurrences happen at correct intervals
      const carReplacements = oneTimeOccurrences.filter(occ => occ.expenseName === 'Car Replacement')
      expect(carReplacements.length).toBe(5) // 25 years / 5 year interval = 5 occurrences
    })

    it('should calculate aggregated results correctly', () => {
      const results = service.calculateAll()
      const { aggregatedResults } = results

      expect(aggregatedResults.totalRetirementCost).toBeGreaterThan(0)
      expect(aggregatedResults.averageAnnualCost).toBeGreaterThan(0)
      expect(aggregatedResults.currentAnnualCost).toBeGreaterThan(0)
      expect(aggregatedResults.breakdown.monthly).toBeGreaterThan(0)
      expect(aggregatedResults.breakdown.yearly).toBeGreaterThan(0)
      expect(aggregatedResults.breakdown.oneTime).toBeGreaterThan(0)
    })
  })

  describe('generateEnhancedProjections', () => {
    it('should generate projections for retirement duration', () => {
      const mockProjectionData = {
        yearlyProjections: [
          {
            age: 65,
            year: 2054,
            portfolioValue: ********,
            realValue: 5000000,
            cumulativeContributions: 3000000,
          },
        ],
        finalValue: ********,
        totalContributions: 3000000,
        monthlyRetirementIncome: 50000,
        realMonthlyIncome: 40000,
        currentAge: 35,
      }

      const oneTimeOccurrences = [
        {
          age: 70,
          year: 2059,
          expenseName: 'Car Replacement',
          amount: 800000,
          totalAmount: 800000,
        },
      ]

      const projections = service.generateEnhancedProjections(
        mockProjectionData,
        oneTimeOccurrences,
        600000 // adjustedAnnualCost
      )

      expect(projections.length).toBe(26) // 1 initial + 25 retirement years
      
      // Check that retirement years are marked correctly
      const retirementYears = projections.filter(p => p.isRetirement)
      expect(retirementYears.length).toBe(25)
    })

    it('should account for one-time costs in projections', () => {
      const mockProjectionData = {
        yearlyProjections: [
          {
            age: 65,
            year: 2054,
            portfolioValue: ********,
            realValue: 5000000,
            cumulativeContributions: 3000000,
          },
        ],
        finalValue: ********,
        totalContributions: 3000000,
        monthlyRetirementIncome: 50000,
        realMonthlyIncome: 40000,
        currentAge: 35,
      }

      const oneTimeOccurrences = [
        {
          age: 70,
          year: 2059,
          expenseName: 'Car Replacement',
          amount: 800000,
          totalAmount: 800000,
        },
      ]

      const projections = service.generateEnhancedProjections(
        mockProjectionData,
        oneTimeOccurrences,
        600000
      )

      // Find the year with one-time cost
      const yearWithOneTimeCost = projections.find(p => p.age === 70)
      expect(yearWithOneTimeCost?.oneTimeCosts).toBe(800000)
      expect(yearWithOneTimeCost?.consolidatedCosts).toBeGreaterThan(600000)
    })
  })

  describe('utility functions', () => {
    it('calculateCosts should return CostResults', () => {
      const results = calculateCosts(sampleExpenses, 65, 25, 35, 3, 12)
      
      expect(results).toHaveProperty('currentMonthlyCost')
      expect(results).toHaveProperty('adjustedMonthlyCost')
      expect(results).toHaveProperty('totalRetirementCost')
      expect(results.currentMonthlyCost).toBeGreaterThan(0)
    })

    it('calculateYearlyBreakdowns should return breakdown array', () => {
      const breakdowns = calculateYearlyBreakdowns(sampleExpenses, 65, 25, 35, 3, 12)
      
      expect(Array.isArray(breakdowns)).toBe(true)
      expect(breakdowns.length).toBe(25)
      expect(breakdowns[0]).toHaveProperty('year')
      expect(breakdowns[0]).toHaveProperty('age')
      expect(breakdowns[0]).toHaveProperty('totalYearlyExpenses')
    })
  })

  describe('edge cases', () => {
    it('should handle empty expenses array', () => {
      const emptyService = new CalculationService([], 65, 25, 35, 3, 12)
      const results = emptyService.calculateAll()
      
      expect(results.aggregatedResults.totalRetirementCost).toBe(0)
      expect(results.yearlyBreakdowns.length).toBe(25)
    })

    it('should handle zero retirement duration', () => {
      const zeroService = new CalculationService(sampleExpenses, 65, 0, 35, 3, 12)
      const results = zeroService.calculateAll()
      
      expect(results.yearlyBreakdowns.length).toBe(0)
      expect(results.aggregatedResults.totalRetirementCost).toBe(0)
    })

    it('should handle one-time expenses with no interval', () => {
      const expensesWithoutInterval: ExpenseCategory[] = [
        {
          id: '1',
          name: 'Invalid One-time',
          iconName: 'AlertTriangle',
          monthlyAmount: 100000,
          frequency: 'one-time',
          // yearsInterval is undefined
        },
      ]

      const invalidService = new CalculationService(expensesWithoutInterval, 65, 25, 35, 3, 12)
      const results = invalidService.calculateAll()
      
      // Should not crash and should handle gracefully
      expect(results).toBeDefined()
      expect(results.oneTimeOccurrences.length).toBe(0)
    })
  })
})
