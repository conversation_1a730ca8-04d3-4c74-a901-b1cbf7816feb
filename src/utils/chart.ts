import { ProjectionPoint, OneTimeOccurrence } from "@/utils/calculationService";
import { ExpenseCategory } from "@/types/costCalculation";
import { INFLATION_RATE } from "@/constants/finance";

export type ChartDatum = {
  age: number;
  year: number;
  "Portfolio Value": number;
  "Real Value": number;
  "Cumulative Contributions": number;
  "Annual Costs": number;
  "One-Time Costs": number;
  phase: "Retirement" | "Accumulation";
};

export const SERIES_KEYS = {
  portfolioValue: "Portfolio Value",
  realValue: "Real Value",
  cumulativeContributions: "Cumulative Contributions",
  annualCosts: "Annual Costs",
  oneTimeCosts: "One-Time Costs",
} as const;

export const AXIS_LABELS = {
  xAge: "Age",
  yValueCurrency: "Value (₹)",
} as const;

export function toChartData(points: ProjectionPoint[]): ChartDatum[] {
  return points.map((projection: ProjectionPoint) => ({
    age: projection.age,
    year: projection.year,
    [SERIES_KEYS.portfolioValue]: projection.portfolioValue,
    [SERIES_KEYS.realValue]: projection.realValue,
    [SERIES_KEYS.cumulativeContributions]: projection.cumulativeContributions,
    [SERIES_KEYS.annualCosts]: projection.annualCost || 0,
    [SERIES_KEYS.oneTimeCosts]: projection.oneTimeCosts || 0,
    phase: projection.isRetirement ? "Retirement" : "Accumulation",
  }));
}

export function getFundDepletionAge(
  chartData: ChartDatum[]
): number | undefined {
  return chartData.find((item) => item[SERIES_KEYS.portfolioValue] <= 0)?.age;
}

export function getOneTimeCostDetailsForAge(
  occurrences: OneTimeOccurrence[],
  age: number
) {
  return occurrences.filter((occurrence) => occurrence.age === age);
}

export function getYearlyExpensesForAge(
  expenses: ExpenseCategory[],
  age: number,
  retirementAge: number,
  currentAge: number,
  inflationRate: number = INFLATION_RATE * 100 // Default to percentage format
): Array<{ name: string; amount: number; frequency: string }> {
  const yearsToRetirement = retirementAge - currentAge;
  const retirementYear = age - retirementAge;

  if (retirementYear < 0) {
    // Not in retirement yet, no expenses
    return [];
  }

  const yearInflationMultiplier = Math.pow(
    1 + inflationRate / 100, // Convert percentage to decimal
    yearsToRetirement + retirementYear
  );
  const inflationMultiplierAtRetirement = Math.pow(
    1 + inflationRate / 100, // Convert percentage to decimal
    yearsToRetirement
  );

  const yearlyExpenses: Array<{
    name: string;
    amount: number;
    frequency: string;
  }> = [];

  expenses.forEach((expense) => {
    if (expense.frequency === "monthly") {
      // Monthly expenses occur every year
      const adjustedMonthlyCost =
        expense.monthlyAmount * inflationMultiplierAtRetirement;
      const yearlyMonthlyCost =
        adjustedMonthlyCost * yearInflationMultiplier * 12;
      yearlyExpenses.push({
        name: expense.name,
        amount: yearlyMonthlyCost,
        frequency: "monthly",
      });
    } else if (expense.frequency === "yearly") {
      // Yearly expenses occur once per year
      const adjustedYearlyCost =
        expense.monthlyAmount * inflationMultiplierAtRetirement;
      const yearlyCost = adjustedYearlyCost * yearInflationMultiplier;
      yearlyExpenses.push({
        name: expense.name,
        amount: yearlyCost,
        frequency: "yearly",
      });
    } else if (expense.frequency === "one-time" && expense.yearsInterval) {
      // Check if this one-time expense occurs in this year
      const stepYears = Math.max(1, expense.yearsInterval);
      if (retirementYear > 0 && retirementYear % stepYears === 0) {
        const adjustedOneTimeCost =
          expense.monthlyAmount * yearInflationMultiplier;
        yearlyExpenses.push({
          name: expense.name,
          amount: adjustedOneTimeCost,
          frequency: "one-time",
        });
      }
    }
  });

  return yearlyExpenses;
}
