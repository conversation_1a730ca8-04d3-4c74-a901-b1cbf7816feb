import { CalculationService, calculateCosts } from "./calculationService";
import { ExpenseCategory } from "@/types/costCalculation";

// Test data
const sampleExpenses: ExpenseCategory[] = [
  {
    id: "1",
    name: "Monthly Rent",
    iconName: "Home",
    monthlyAmount: 15000,
    frequency: "monthly",
  },
  {
    id: "2",
    name: "Yearly Insurance",
    iconName: "Shield",
    monthlyAmount: 24000, // This represents yearly amount
    frequency: "yearly",
  },
  {
    id: "3",
    name: "Car Replacement",
    iconName: "Car",
    monthlyAmount: 500000, // This represents cost per occurrence
    frequency: "one-time",
    yearsInterval: 10,
  },
];

const testCalculationService = () => {
  console.log("Testing Calculation Service...");

  const service = new CalculationService(
    sampleExpenses,
    60, // retirement age
    25, // retirement duration
    30, // current age
    6, // inflation rate
    12 // annual return
  );

  // Test: Complete calculation using calculateAll()
  console.log("\n=== Complete Calculation Test ===");
  const allResults = service.calculateAll();

  console.log(
    `Generated ${allResults.yearlyBreakdowns.length} yearly breakdowns`
  );

  // Show first year breakdown
  if (allResults.yearlyBreakdowns.length > 0) {
    const firstYear = allResults.yearlyBreakdowns[0];
    console.log("First year of retirement:", {
      year: firstYear.year,
      age: firstYear.age,
      monthlyExpenses: firstYear.monthlyExpenses,
      yearlyExpenses: firstYear.yearlyExpenses,
      oneTimeExpenses: firstYear.oneTimeExpenses,
      totalYearlyExpenses: firstYear.totalYearlyExpenses,
    });
  }

  console.log("Aggregated results:", allResults.aggregatedResults);

  console.log("Cost results:", {
    currentMonthlyCost: allResults.costResults.currentMonthlyCost,
    currentAnnualCost: allResults.costResults.currentAnnualCost,
    adjustedMonthlyCost: allResults.costResults.adjustedMonthlyCost,
    adjustedAnnualCost: allResults.costResults.adjustedAnnualCost,
    totalRetirementCost: allResults.costResults.totalRetirementCost,
    oneTimeCosts: allResults.costResults.oneTimeCosts,
  });

  console.log(
    `Generated ${allResults.oneTimeOccurrences.length} one-time occurrences`
  );
  allResults.oneTimeOccurrences.forEach((occurrence, index) => {
    console.log(`Occurrence ${index + 1}:`, {
      age: occurrence.age,
      year: occurrence.year,
      expenseName: occurrence.expenseName,
      amount: occurrence.amount,
    });
  });

  console.log("All results calculated successfully:", {
    yearlyBreakdownsCount: allResults.yearlyBreakdowns.length,
    aggregatedResults: allResults.aggregatedResults,
    costResults: allResults.costResults,
    oneTimeOccurrencesCount: allResults.oneTimeOccurrences.length,
  });

  // Test utility functions
  console.log("\n=== Utility Functions ===");
  const costResultsFromUtility = calculateCosts(
    sampleExpenses,
    60,
    25,
    30,
    6,
    12
  );
  console.log("Utility function result:", costResultsFromUtility);

  console.log("\n✅ All tests completed successfully!");
};

// Export for use in development
export { testCalculationService };
