// Test utility to verify error fixes work correctly
import { getSafeDetailedBreakdown, hasDetailedBreakdown, migrateCostResults, isValidCostResults } from './costDataHelpers';
import { CostResults } from '@/types/costCalculation';

export const testErrorFixes = () => {
  console.log('=== Testing Error Fixes ===');

  // Test 1: Null/undefined data
  console.log('\n1. Testing null/undefined data:');
  const safeBreakdownNull = getSafeDetailedBreakdown(null);
  console.log('Null data breakdown:', safeBreakdownNull);
  console.log('Has valid breakdown (null):', hasDetailedBreakdown(null));

  // Test 2: Old data structure without detailedBreakdown
  console.log('\n2. Testing old data structure:');
  const oldData = {
    currentMonthlyCost: 10000,
    currentAnnualCost: 120000,
    adjustedMonthlyCost: 15000,
    adjustedAnnualCost: 180000,
    totalRetirementCost: 5000000,
    oneTimeCosts: 500000,
    breakdown: {
      monthly: 4500000,
      yearly: 0,
      oneTime: 500000,
    },
    // Missing detailedBreakdown
  };

  console.log('Is valid old data:', isValidCostResults(oldData));
  const migratedData = migrateCostResults(oldData as any);
  console.log('Migrated data:', migratedData);
  console.log('Has valid breakdown after migration:', hasDetailedBreakdown(migratedData));

  // Test 3: Safe access with old data
  console.log('\n3. Testing safe access:');
  const safeBreakdownOld = getSafeDetailedBreakdown(oldData as any);
  console.log('Safe breakdown from old data:', safeBreakdownOld);

  // Test 4: New data structure
  console.log('\n4. Testing new data structure:');
  const newData: CostResults = {
    currentMonthlyCost: 10000,
    currentAnnualCost: 120000,
    adjustedMonthlyCost: 15000,
    adjustedAnnualCost: 180000,
    totalRetirementCost: 5000000,
    oneTimeCosts: 500000,
    breakdown: {
      monthly: 4500000,
      yearly: 0,
      oneTime: 500000,
    },
    detailedBreakdown: {
      currentYearlyCost: 50000,
      adjustedYearlyCost: 75000,
      yearlyExpensesByCategory: [
        {
          name: 'Vacation',
          currentAmount: 50000,
          adjustedAmount: 75000,
          frequency: 'yearly'
        }
      ],
      oneTimeExpensesByCategory: [
        {
          name: 'Laptop',
          costPerOccurrence: 80000,
          yearsInterval: 4,
          totalOccurrences: 6,
          totalCost: 500000,
          frequency: 'one-time'
        }
      ]
    }
  };

  console.log('Is valid new data:', isValidCostResults(newData));
  console.log('Has valid breakdown (new):', hasDetailedBreakdown(newData));
  const safeBreakdownNew = getSafeDetailedBreakdown(newData);
  console.log('Safe breakdown from new data:', safeBreakdownNew);

  // Test 5: Malformed data
  console.log('\n5. Testing malformed data:');
  const malformedData = {
    currentMonthlyCost: 10000,
    detailedBreakdown: 'not an object', // Wrong type
  };

  console.log('Is valid malformed data:', isValidCostResults(malformedData));
  const safeBreakdownMalformed = getSafeDetailedBreakdown(malformedData as any);
  console.log('Safe breakdown from malformed data:', safeBreakdownMalformed);

  console.log('\n=== All Tests Complete ===');
  
  return {
    nullTest: safeBreakdownNull,
    oldDataTest: { original: oldData, migrated: migratedData },
    newDataTest: safeBreakdownNew,
    malformedTest: safeBreakdownMalformed
  };
};

// Export for console testing
if (typeof window !== 'undefined') {
  (window as any).testErrorFixes = testErrorFixes;
}
