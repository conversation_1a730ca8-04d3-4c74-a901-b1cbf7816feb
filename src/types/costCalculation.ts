export interface ExpenseCategory {
  id: string;
  name: string;
  iconName: string;
  monthlyAmount: number; // For monthly expenses, this is the monthly amount
  frequency: "monthly" | "yearly" | "one-time";
  yearsInterval?: number; // Required for one-time expenses, represents interval in years
  isCustom?: boolean;
  // Note: For yearly expenses, monthlyAmount represents the yearly amount
  // For one-time expenses, monthlyAmount represents the cost per occurrence
}

export interface CostCalculationProps {
  retirementAge: number;
  inflationRate: number;
}

export interface CostResults {
  currentMonthlyCost: number;
  currentAnnualCost: number;
  adjustedMonthlyCost: number;
  adjustedAnnualCost: number;
  totalRetirementCost: number;
  oneTimeCosts: number;
  breakdown: {
    monthly: number;
    yearly: number;
    oneTime: number;
  };
  // Additional detailed breakdown for projections
  detailedBreakdown: {
    currentYearlyCost: number;
    adjustedYearlyCost: number;
    yearlyExpensesByCategory: Array<{
      name: string;
      currentAmount: number;
      adjustedAmount: number;
      frequency: "yearly";
    }>;
    oneTimeExpensesByCategory: Array<{
      name: string;
      costPerOccurrence: number;
      yearsInterval: number;
      totalOccurrences: number;
      totalCost: number;
      frequency: "one-time";
    }>;
  };
}
